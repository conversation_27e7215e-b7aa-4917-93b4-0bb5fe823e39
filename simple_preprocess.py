#!/usr/bin/env python3
"""
Simple preprocessing script using AudioPreprocessor directly.
"""

import sys
import logging
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, '.')

from audtecheq.preprocessing import AudioPreprocessor
from audtecheq.core.config import PreprocessingConfig

def main():
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Test with one file first
    input_file = Path("audtecheq/data/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicX.wav")
    output_file = Path("audtecheq/data/derivative/preprocessed/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicX.wav")
    
    if not input_file.exists():
        logger.error(f"Input file not found: {input_file}")
        return 1
    
    # Create output directory
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    # Create preprocessing configuration
    config = PreprocessingConfig(
        target_sample_rate=16000,
        target_channels=1,
        target_dbfs=-20.0,
        noise_reduction_prop=0.8
    )
    
    # Initialize preprocessor
    preprocessor = AudioPreprocessor(config=config)
    
    try:
        logger.info(f"Processing: {input_file}")
        result = preprocessor.process(input_file, output_file)
        logger.info(f"Success! Output: {result['output_file']}")
        return 0
    except Exception as e:
        logger.error(f"Processing failed: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
