{"participant_id": "sub-ATL004", "total_files": 20, "successful_files": 20, "failed_files": 0, "file_results": [{"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-MicX.wav", "file_stem": "sub-ATL004_task-gfta.test_acq-MicX", "success": true, "steps_completed": ["audio_analysis", "save_analysis"], "errors": [], "file_info": {"size_bytes": 118407012, "size_mb": 112.92, "duration_seconds": 822.238, "duration_minutes": 13.7, "sample_rate": 48000, "channels": 1, "sample_width": 4, "frame_count": 39467401.0, "max_dBFS": -1.0313941115556916e-06, "rms_dBFS": -21.992235875151948}, "analysis_file": "data_analysis/sub-ATL004/analysis/sub-ATL004_task-gfta.test_acq-MicX_analysis.json"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-MicXY.wav", "file_stem": "sub-ATL004_task-gfta.test_acq-MicXY", "success": true, "steps_completed": ["audio_analysis", "save_analysis"], "errors": [], "file_info": {"size_bytes": 118407012, "size_mb": 112.92, "duration_seconds": 822.238, "duration_minutes": 13.7, "sample_rate": 48000, "channels": 1, "sample_width": 4, "frame_count": 39467401.0, "max_dBFS": -5.462041407975743, "rms_dBFS": -39.300235549027846}, "analysis_file": "data_analysis/sub-ATL004/analysis/sub-ATL004_task-gfta.test_acq-MicXY_analysis.json"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-MicY.wav", "file_stem": "sub-ATL004_task-gfta.test_acq-MicY", "success": true, "steps_completed": ["audio_analysis", "save_analysis"], "errors": [], "file_info": {"size_bytes": 118407012, "size_mb": 112.92, "duration_seconds": 822.238, "duration_minutes": 13.7, "sample_rate": 48000, "channels": 1, "sample_width": 4, "frame_count": 39467401.0, "max_dBFS": -1.035438794587188e-06, "rms_dBFS": -43.8904110588281}, "analysis_file": "data_analysis/sub-ATL004/analysis/sub-ATL004_task-gfta.test_acq-MicY_analysis.json"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-ZA.wav", "file_stem": "sub-ATL004_task-gfta.test_acq-ZA", "success": true, "steps_completed": ["audio_analysis", "save_analysis"], "errors": [], "file_info": {"size_bytes": 165318700, "size_mb": 157.66, "duration_seconds": 861.035, "duration_minutes": 14.35, "sample_rate": 48000, "channels": 2, "sample_width": 2, "frame_count": 41329664.0, "max_dBFS": -2.1601608000251487, "rms_dBFS": -25.14104261728418}, "analysis_file": "data_analysis/sub-ATL004/analysis/sub-ATL004_task-gfta.test_acq-ZA_analysis.json"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-MicX.wav", "file_stem": "sub-ATL004_task-picturetask_acq-MicX", "success": true, "steps_completed": ["audio_analysis", "save_analysis"], "errors": [], "file_info": {"size_bytes": 71454564, "size_mb": 68.14, "duration_seconds": 496.179, "duration_minutes": 8.27, "sample_rate": 48000, "channels": 1, "sample_width": 4, "frame_count": 23816585.0, "max_dBFS": -1.0313941115556916e-06, "rms_dBFS": -21.106174688733084}, "analysis_file": "data_analysis/sub-ATL004/analysis/sub-ATL004_task-picturetask_acq-MicX_analysis.json"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-MicXY.wav", "file_stem": "sub-ATL004_task-picturetask_acq-MicXY", "success": true, "steps_completed": ["audio_analysis", "save_analysis"], "errors": [], "file_info": {"size_bytes": 71454564, "size_mb": 68.14, "duration_seconds": 496.179, "duration_minutes": 8.27, "sample_rate": 48000, "channels": 1, "sample_width": 4, "frame_count": 23816585.0, "max_dBFS": -5.045423625965188, "rms_dBFS": -37.174139368797185}, "analysis_file": "data_analysis/sub-ATL004/analysis/sub-ATL004_task-picturetask_acq-MicXY_analysis.json"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-MicY.wav", "file_stem": "sub-ATL004_task-picturetask_acq-MicY", "success": true, "steps_completed": ["audio_analysis", "save_analysis"], "errors": [], "file_info": {"size_bytes": 71454564, "size_mb": 68.14, "duration_seconds": 496.179, "duration_minutes": 8.27, "sample_rate": 48000, "channels": 1, "sample_width": 4, "frame_count": 23816585.0, "max_dBFS": -1.0313941115556916e-06, "rms_dBFS": -36.031395541494305}, "analysis_file": "data_analysis/sub-ATL004/analysis/sub-ATL004_task-picturetask_acq-MicY_analysis.json"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-ZA.wav", "file_stem": "sub-ATL004_task-picturetask_acq-ZA", "success": true, "steps_completed": ["audio_analysis", "save_analysis"], "errors": [], "file_info": {"size_bytes": 94576684, "size_mb": 90.2, "duration_seconds": 492.587, "duration_minutes": 8.21, "sample_rate": 48000, "channels": 2, "sample_width": 2, "frame_count": 23644160.0, "max_dBFS": -2.9231969104207445, "rms_dBFS": -24.624897345158473}, "analysis_file": "data_analysis/sub-ATL004/analysis/sub-ATL004_task-picturetask_acq-ZA_analysis.json"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-MicX.wav", "file_stem": "sub-ATL004_task-pls5.screen_acq-MicX", "success": true, "steps_completed": ["audio_analysis", "save_analysis"], "errors": [], "file_info": {"size_bytes": 70466916, "size_mb": 67.2, "duration_seconds": 489.32, "duration_minutes": 8.16, "sample_rate": 48000, "channels": 1, "sample_width": 4, "frame_count": 23487369.0, "max_dBFS": -1.0313941115556916e-06, "rms_dBFS": -24.740906117097193}, "analysis_file": "data_analysis/sub-ATL004/analysis/sub-ATL004_task-pls5.screen_acq-MicX_analysis.json"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-MicXY.wav", "file_stem": "sub-ATL004_task-pls5.screen_acq-MicXY", "success": true, "steps_completed": ["audio_analysis", "save_analysis"], "errors": [], "file_info": {"size_bytes": 70466916, "size_mb": 67.2, "duration_seconds": 489.32, "duration_minutes": 8.16, "sample_rate": 48000, "channels": 1, "sample_width": 4, "frame_count": 23487369.0, "max_dBFS": -8.46782774544279, "rms_dBFS": -37.73348904148318}, "analysis_file": "data_analysis/sub-ATL004/analysis/sub-ATL004_task-pls5.screen_acq-MicXY_analysis.json"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-MicY.wav", "file_stem": "sub-ATL004_task-pls5.screen_acq-MicY", "success": true, "steps_completed": ["audio_analysis", "save_analysis"], "errors": [], "file_info": {"size_bytes": 70466916, "size_mb": 67.2, "duration_seconds": 489.32, "duration_minutes": 8.16, "sample_rate": 48000, "channels": 1, "sample_width": 4, "frame_count": 23487369.0, "max_dBFS": -0.9382837604985063, "rms_dBFS": -39.69470781624289}, "analysis_file": "data_analysis/sub-ATL004/analysis/sub-ATL004_task-pls5.screen_acq-MicY_analysis.json"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-ZA.wav", "file_stem": "sub-ATL004_task-pls5.screen_acq-ZA", "success": true, "steps_completed": ["audio_analysis", "save_analysis"], "errors": [], "file_info": {"size_bytes": 91828268, "size_mb": 87.57, "duration_seconds": 478.272, "duration_minutes": 7.97, "sample_rate": 48000, "channels": 2, "sample_width": 2, "frame_count": 22957056.0, "max_dBFS": -2.0101999108339896, "rms_dBFS": -25.26164840999638}, "analysis_file": "data_analysis/sub-ATL004/analysis/sub-ATL004_task-pls5.screen_acq-ZA_analysis.json"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-MicX.wav", "file_stem": "sub-ATL004_task-toyplay_acq-MicX", "success": true, "steps_completed": ["audio_analysis", "save_analysis"], "errors": [], "file_info": {"size_bytes": 89756004, "size_mb": 85.6, "duration_seconds": 623.272, "duration_minutes": 10.39, "sample_rate": 48000, "channels": 1, "sample_width": 4, "frame_count": 29917065.0, "max_dBFS": -1.0313941115556916e-06, "rms_dBFS": -22.06860272338237}, "analysis_file": "data_analysis/sub-ATL004/analysis/sub-ATL004_task-toyplay_acq-MicX_analysis.json"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-MicXY.wav", "file_stem": "sub-ATL004_task-toyplay_acq-MicXY", "success": true, "steps_completed": ["audio_analysis", "save_analysis"], "errors": [], "file_info": {"size_bytes": 89756004, "size_mb": 85.6, "duration_seconds": 623.272, "duration_minutes": 10.39, "sample_rate": 48000, "channels": 1, "sample_width": 4, "frame_count": 29917065.0, "max_dBFS": -1.035438794587188e-06, "rms_dBFS": -33.653532303213744}, "analysis_file": "data_analysis/sub-ATL004/analysis/sub-ATL004_task-toyplay_acq-MicXY_analysis.json"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-MicY.wav", "file_stem": "sub-ATL004_task-toyplay_acq-MicY", "success": true, "steps_completed": ["audio_analysis", "save_analysis"], "errors": [], "file_info": {"size_bytes": 89756004, "size_mb": 85.6, "duration_seconds": 623.272, "duration_minutes": 10.39, "sample_rate": 48000, "channels": 1, "sample_width": 4, "frame_count": 29917065.0, "max_dBFS": -1.0313941115556916e-06, "rms_dBFS": -35.286938471353466}, "analysis_file": "data_analysis/sub-ATL004/analysis/sub-ATL004_task-toyplay_acq-MicY_analysis.json"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-ZA.wav", "file_stem": "sub-ATL004_task-toyplay_acq-ZA", "success": true, "steps_completed": ["audio_analysis", "save_analysis"], "errors": [], "file_info": {"size_bytes": 113741868, "size_mb": 108.47, "duration_seconds": 592.405, "duration_minutes": 9.87, "sample_rate": 48000, "channels": 2, "sample_width": 2, "frame_count": 28435456.0, "max_dBFS": -2.9276515936390295, "rms_dBFS": -25.885932260100255}, "analysis_file": "data_analysis/sub-ATL004/analysis/sub-ATL004_task-toyplay_acq-ZA_analysis.json"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-warmup_acq-MicX.wav", "file_stem": "sub-ATL004_task-warmup_acq-MicX", "success": true, "steps_completed": ["audio_analysis", "save_analysis"], "errors": [], "file_info": {"size_bytes": 105114468, "size_mb": 100.24, "duration_seconds": 729.928, "duration_minutes": 12.17, "sample_rate": 48000, "channels": 1, "sample_width": 4, "frame_count": 35036553.0, "max_dBFS": -1.0313941115556916e-06, "rms_dBFS": -23.09404249150554}, "analysis_file": "data_analysis/sub-ATL004/analysis/sub-ATL004_task-warmup_acq-MicX_analysis.json"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-warmup_acq-MicXY.wav", "file_stem": "sub-ATL004_task-warmup_acq-MicXY", "success": true, "steps_completed": ["audio_analysis", "save_analysis"], "errors": [], "file_info": {"size_bytes": 105114468, "size_mb": 100.24, "duration_seconds": 729.928, "duration_minutes": 12.17, "sample_rate": 48000, "channels": 1, "sample_width": 4, "frame_count": 35036553.0, "max_dBFS": -0.23710108034295033, "rms_dBFS": -32.694464471767844}, "analysis_file": "data_analysis/sub-ATL004/analysis/sub-ATL004_task-warmup_acq-MicXY_analysis.json"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-warmup_acq-MicY.wav", "file_stem": "sub-ATL004_task-warmup_acq-MicY", "success": true, "steps_completed": ["audio_analysis", "save_analysis"], "errors": [], "file_info": {"size_bytes": 105114468, "size_mb": 100.24, "duration_seconds": 729.928, "duration_minutes": 12.17, "sample_rate": 48000, "channels": 1, "sample_width": 4, "frame_count": 35036553.0, "max_dBFS": -1.0313941115556916e-06, "rms_dBFS": -35.11899204350826}, "analysis_file": "data_analysis/sub-ATL004/analysis/sub-ATL004_task-warmup_acq-MicY_analysis.json"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-warmup_acq-ZA.wav", "file_stem": "sub-ATL004_task-warmup_acq-ZA", "success": true, "steps_completed": ["audio_analysis", "save_analysis"], "errors": [], "file_info": {"size_bytes": 127995948, "size_mb": 122.07, "duration_seconds": 666.645, "duration_minutes": 11.11, "sample_rate": 48000, "channels": 2, "sample_width": 2, "frame_count": 31998976.0, "max_dBFS": -2.5331252527243264, "rms_dBFS": -25.797644430404937}, "analysis_file": "data_analysis/sub-ATL004/analysis/sub-ATL004_task-warmup_acq-ZA_analysis.json"}], "summary": {"success_rate": 1.0, "total_duration_minutes": 209.56, "total_size_mb": 1868.3, "unique_sample_rates": [48000], "average_file_duration_minutes": 10.48}}