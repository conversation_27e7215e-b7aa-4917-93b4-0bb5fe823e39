#!/usr/bin/env python3
"""
Batch processing script for all AudioTechEquity participants.

This script runs the enhanced AudioTechEquity pipeline for all participants
in the data directory, organizing outputs and generating comprehensive reports.
"""

import argparse
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple

# Add current directory to path for imports
sys.path.insert(0, '.')

from audtecheq.core.data_manager import DataManager
from audtecheq.preprocessing import AudioPreprocessor
from audtecheq.vad import VADProcessor, SilenceProcessor
from audtecheq.diarization import SpeakerDiarizer
from audtecheq.testing import EnhancedDiarizationTester


def setup_logging(log_level: str = "INFO") -> None:
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'batch_processing_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        ]
    )


def discover_participants(data_dir: Path) -> Dict[str, List[Path]]:
    """
    Discover all participants and their audio files.

    Parameters
    ----------
    data_dir : Path
        Base data directory

    Returns
    -------
    dict
        Dictionary mapping participant IDs to lists of audio files
    """
    participants = {}

    for participant_dir in data_dir.glob("sub-ATL*"):
        if participant_dir.is_dir():
            participant_id = participant_dir.name
            audio_files = []

            # Find all WAV files in behavioral data
            beh_dir = participant_dir / "beh"
            if beh_dir.exists():
                audio_files.extend(list(beh_dir.glob("*.wav")))

            if audio_files:
                participants[participant_id] = sorted(audio_files)

    return participants


def process_single_file(audio_file: Path, output_base: Path,
                       configs: Dict, logger) -> Dict:
    """
    Process a single audio file through the complete pipeline.

    Parameters
    ----------
    audio_file : Path
        Path to audio file
    output_base : Path
        Base output directory
    configs : dict
        Processing configurations
    logger : logging.Logger
        Logger instance

    Returns
    -------
    dict
        Processing results
    """
    file_stem = audio_file.stem
    logger.info(f"🎵 Processing: {file_stem}")

    results = {
        "input_file": str(audio_file),
        "file_stem": file_stem,
        "success": False,
        "steps_completed": [],
        "errors": []
    }

    try:
        current_input = audio_file

        # Step 1: Preprocessing
        logger.info(f"  📊 Step 1: Preprocessing")
        preprocessor = AudioPreprocessor()
        preprocess_output = output_base / "preprocessed" / f"{file_stem}_preprocessed.wav"
        preprocess_output.parent.mkdir(parents=True, exist_ok=True)

        preprocess_result = preprocessor.process(current_input, preprocess_output)
        results["preprocessing"] = preprocess_result
        results["steps_completed"].append("preprocessing")
        current_input = Path(preprocess_result['output_file'])

        # Step 2: VAD
        logger.info(f"  🎤 Step 2: Voice Activity Detection")
        vad_processor = VADProcessor()
        vad_output = output_base / "vad" / f"{file_stem}_vad.wav"
        vad_output.parent.mkdir(parents=True, exist_ok=True)

        vad_result = vad_processor.process(current_input, vad_output)
        results["vad"] = vad_result
        results["steps_completed"].append("vad")
        current_input = Path(vad_result['output_file'])

        # Step 3: Silence Processing
        logger.info(f"  🔇 Step 3: Discard Long Silences")
        silence_processor = SilenceProcessor()
        silence_output = output_base / "silence_processed" / f"{file_stem}_silence.wav"
        silence_output.parent.mkdir(parents=True, exist_ok=True)

        # Use VAD timestamps if available
        vad_timestamps_path = None
        vad_timestamps_file = output_base / "vad" / f"{file_stem}_vad_timestamps.json"
        if vad_timestamps_file.exists():
            vad_timestamps_path = vad_timestamps_file

        silence_result = silence_processor.process(
            current_input,
            silence_output,
            vad_timestamps_path=vad_timestamps_path,
            max_silence_duration=2.0,
            preserve_boundaries=True
        )
        results["silence_processing"] = silence_result
        results["steps_completed"].append("silence_processing")
        current_input = Path(silence_result['output_file'])

        # Step 4: Diarization
        logger.info(f"  👥 Step 4: Speaker Diarization")
        diarizer = SpeakerDiarizer()
        diarization_output = output_base / "diarization" / file_stem
        diarization_output.mkdir(parents=True, exist_ok=True)

        diarization_result = diarizer.process(current_input, diarization_output)
        results["diarization"] = diarization_result
        results["steps_completed"].append("diarization")

        # Step 5: Enhanced Testing
        logger.info(f"  🧪 Step 5: Enhanced Diarization Testing")
        tester = EnhancedDiarizationTester()
        test_output = output_base / "test_reports" / f"{file_stem}_enhanced_test.json"
        test_output.parent.mkdir(parents=True, exist_ok=True)

        test_result = tester.process(
            diarization_output,
            test_output,
            embeddings_path=None,  # No embeddings available
            expected_speakers=2,   # Assume 2 speakers (participant + examiner)
            max_speakers_threshold=4,
            enable_cluster_analysis=False
        )
        results["testing"] = test_result
        results["steps_completed"].append("testing")

        results["success"] = True
        logger.info(f"  ✅ Completed: {file_stem}")

    except Exception as e:
        error_msg = f"Processing failed for {file_stem}: {str(e)}"
        logger.error(f"  ❌ {error_msg}")
        results["errors"].append(error_msg)

    return results


def process_participant(participant_id: str, audio_files: List[Path],
                       output_dir: Path, configs: Dict, logger) -> Dict:
    """
    Process all audio files for a single participant.

    Parameters
    ----------
    participant_id : str
        Participant identifier
    audio_files : list
        List of audio files for this participant
    output_dir : Path
        Output directory
    configs : dict
        Processing configurations
    logger : logging.Logger
        Logger instance

    Returns
    -------
    dict
        Participant processing results
    """
    logger.info(f"🧑 Processing participant: {participant_id}")
    logger.info(f"   Files to process: {len(audio_files)}")

    participant_output = output_dir / participant_id
    participant_output.mkdir(parents=True, exist_ok=True)

    participant_results = {
        "participant_id": participant_id,
        "total_files": len(audio_files),
        "successful_files": 0,
        "failed_files": 0,
        "file_results": [],
        "summary": {}
    }

    for audio_file in audio_files:
        file_result = process_single_file(
            audio_file, participant_output, configs, logger
        )
        participant_results["file_results"].append(file_result)

        if file_result["success"]:
            participant_results["successful_files"] += 1
        else:
            participant_results["failed_files"] += 1

    # Generate participant summary
    participant_results["summary"] = {
        "success_rate": participant_results["successful_files"] / participant_results["total_files"],
        "total_processing_steps": sum(len(fr["steps_completed"]) for fr in participant_results["file_results"]),
        "common_errors": []  # Could analyze common error patterns
    }

    logger.info(f"   ✅ Participant {participant_id} completed:")
    logger.info(f"      Successful: {participant_results['successful_files']}/{participant_results['total_files']}")
    logger.info(f"      Success rate: {participant_results['summary']['success_rate']:.1%}")

    return participant_results


def generate_batch_report(all_results: List[Dict], output_dir: Path, logger) -> None:
    """Generate comprehensive batch processing report."""
    logger.info("📊 Generating batch processing report...")

    # Calculate overall statistics
    total_participants = len(all_results)
    total_files = sum(r["total_files"] for r in all_results)
    total_successful = sum(r["successful_files"] for r in all_results)
    total_failed = sum(r["failed_files"] for r in all_results)

    overall_success_rate = total_successful / total_files if total_files > 0 else 0

    # Create summary report
    report = {
        "batch_processing_summary": {
            "timestamp": datetime.now().isoformat(),
            "total_participants": total_participants,
            "total_files": total_files,
            "successful_files": total_successful,
            "failed_files": total_failed,
            "overall_success_rate": overall_success_rate
        },
        "participant_results": all_results,
        "processing_statistics": {
            "participants_with_100_percent_success": sum(1 for r in all_results if r["summary"]["success_rate"] == 1.0),
            "participants_with_partial_success": sum(1 for r in all_results if 0 < r["summary"]["success_rate"] < 1.0),
            "participants_with_no_success": sum(1 for r in all_results if r["summary"]["success_rate"] == 0.0)
        }
    }

    # Save report
    report_path = output_dir / f"batch_processing_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    import json
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2, default=str)

    logger.info(f"📋 Batch processing report saved: {report_path}")
    logger.info(f"📈 Overall Results:")
    logger.info(f"   Participants: {total_participants}")
    logger.info(f"   Total files: {total_files}")
    logger.info(f"   Success rate: {overall_success_rate:.1%}")


def main():
    """Main batch processing function."""
    parser = argparse.ArgumentParser(
        description="Batch process all AudioTechEquity participants",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    parser.add_argument(
        '--data-dir',
        type=Path,
        default=Path('./audtecheq/data'),
        help='Input data directory containing participants'
    )

    parser.add_argument(
        '--output-dir',
        type=Path,
        default=Path('./data_processed'),
        help='Output directory for processed data'
    )

    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level'
    )

    parser.add_argument(
        '--participants',
        nargs='+',
        help='Specific participants to process (default: all)'
    )

    args = parser.parse_args()

    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)

    logger.info("🚀 Starting AudioTechEquity batch processing")
    logger.info(f"📁 Data directory: {args.data_dir}")
    logger.info(f"📁 Output directory: {args.output_dir}")

    # Initialize data manager
    data_manager = DataManager(args.output_dir)

    # Discover participants
    participants = discover_participants(args.data_dir)

    if not participants:
        logger.error("❌ No participants found in data directory")
        return 1

    # Filter participants if specified
    if args.participants:
        participants = {pid: files for pid, files in participants.items()
                      if pid in args.participants}

    logger.info(f"👥 Found {len(participants)} participants:")
    for pid, files in participants.items():
        logger.info(f"   {pid}: {len(files)} files")

    # Setup processing configurations (using default configs)
    configs = {
        'preprocessing': {
            'target_sample_rate': 16000,
            'target_channels': 1,
            'target_dbfs': -20.0,
            'noise_reduction_prop': 0.8
        },
        'vad': {
            'threshold': 0.5,
            'min_speech_duration_ms': 250,
            'min_silence_duration_ms': 100
        },
        'diarization': {
            'oracle_num_speakers': None,
            'max_num_speakers': 4,
            'clustering_backend': "spectral"
        }
    }

    # Process all participants
    all_results = []

    for participant_id, audio_files in participants.items():
        participant_result = process_participant(
            participant_id, audio_files, args.output_dir, configs, logger
        )
        all_results.append(participant_result)

    # Generate comprehensive report
    generate_batch_report(all_results, args.output_dir, logger)

    logger.info("🎉 Batch processing completed successfully!")

    return 0


if __name__ == "__main__":
    exit(main())
