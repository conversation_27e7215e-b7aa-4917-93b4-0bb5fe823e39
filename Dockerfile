# Generated by: Neurodocker version 0.7.0+0.gdc97516.dirty
# Latest release: Neurodocker version 1.0.1
# Timestamp: 2024/11/26 22:46:57 UTC
# 
# Thank you for using Neurodocker. If you discover any issues
# or ways to improve this software, please submit an issue or
# pull request on our GitHub repository:
# 
#     https://github.com/ReproNim/neurodocker

FROM ubuntu:22.04

USER root

ARG DEBIAN_FRONTEND="noninteractive"

ENV LANG="en_US.UTF-8" \
    LC_ALL="en_US.UTF-8" \
    ND_ENTRYPOINT="/neurodocker/startup.sh"
RUN export ND_ENTRYPOINT="/neurodocker/startup.sh" \
    && apt-get update -qq \
    && apt-get install -y -q --no-install-recommends \
           apt-utils \
           bzip2 \
           ca-certificates \
           curl \
           locales \
           unzip \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && sed -i -e 's/# en_US.UTF-8 UTF-8/en_US.UTF-8 UTF-8/' /etc/locale.gen \
    && dpkg-reconfigure --frontend=noninteractive locales \
    && update-locale LANG="en_US.UTF-8" \
    && chmod 777 /opt && chmod a+s /opt \
    && mkdir -p /neurodocker \
    && if [ ! -f "$ND_ENTRYPOINT" ]; then \
         echo '#!/usr/bin/env bash' >> "$ND_ENTRYPOINT" \
    &&   echo 'set -e' >> "$ND_ENTRYPOINT" \
    &&   echo 'export USER="${USER:=`whoami`}"' >> "$ND_ENTRYPOINT" \
    &&   echo 'if [ -n "$1" ]; then "$@"; else /usr/bin/env bash; fi' >> "$ND_ENTRYPOINT"; \
    fi \
    && chmod -R 777 /neurodocker && chmod a+s /neurodocker

ENTRYPOINT ["/neurodocker/startup.sh"]

ENV DEBIAN_FRONTEND="noninteractive"

ENV CONDA_DIR="/opt/miniconda-latest" \
    PATH="/opt/miniconda-latest/bin:$PATH"
RUN export PATH="/opt/miniconda-latest/bin:$PATH" \
    && echo "Downloading Miniconda installer ..." \
    && conda_installer="/tmp/miniconda.sh" \
    && curl -fsSL --retry 5 -o "$conda_installer" https://repo.continuum.io/miniconda/Miniconda3-latest-Linux-x86_64.sh \
    && bash "$conda_installer" -b -p /opt/miniconda-latest \
    && rm -f "$conda_installer" \
    && conda update -yq -nbase conda \
    && conda config --system --prepend channels conda-forge \
    && conda config --system --set auto_update_conda false \
    && conda config --system --set show_channel_urls true \
    && sync && conda clean -y --all && sync \
    && conda create -y -q --name audtecheq \
    && conda install -y -q --name audtecheq \
           "python=3.11" \
    && sync && conda clean -y --all && sync \
    && sed -i '$isource activate audtecheq' $ND_ENTRYPOINT

COPY [".", "/home/<USER>"]

RUN bash -c 'conda activate audtecheq && cd /home/<USER>'

ENV IS_DOCKER="1"

WORKDIR /tmp/

ENTRYPOINT ["/neurodocker/startup.sh", "audtecheq"]

RUN echo '{ \
    \n  "pkg_manager": "apt", \
    \n  "instructions": [ \
    \n    [ \
    \n      "base", \
    \n      "ubuntu:22.04" \
    \n    ], \
    \n    [ \
    \n      "env", \
    \n      { \
    \n        "DEBIAN_FRONTEND": "noninteractive" \
    \n      } \
    \n    ], \
    \n    [ \
    \n      "miniconda", \
    \n      { \
    \n        "version": "latest", \
    \n        "conda_install": [ \
    \n          "python=3.11" \
    \n        ], \
    \n        "create_env": "audtecheq", \
    \n        "activate": true \
    \n      } \
    \n    ], \
    \n    [ \
    \n      "copy", \
    \n      [ \
    \n        ".", \
    \n        "/home/<USER>" \
    \n      ] \
    \n    ], \
    \n    [ \
    \n      "run_bash", \
    \n      "conda activate audtecheq && cd /home/<USER>" \
    \n    ], \
    \n    [ \
    \n      "env", \
    \n      { \
    \n        "IS_DOCKER": "1" \
    \n      } \
    \n    ], \
    \n    [ \
    \n      "workdir", \
    \n      "/tmp/" \
    \n    ], \
    \n    [ \
    \n      "entrypoint", \
    \n      "/neurodocker/startup.sh audtecheq" \
    \n    ] \
    \n  ] \
    \n}' > /neurodocker/neurodocker_specs.json
