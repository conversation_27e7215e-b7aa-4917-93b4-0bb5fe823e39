#!/usr/bin/env python3
"""
Example usage of AudioTechEquity pipeline components.

This script demonstrates how to use the AudioTechEquity pipeline both
programmatically and via command line interface.
"""

import logging
from pathlib import Path

# Import AudioTechEquity components
from audtecheq import (
    PipelineConfig,
    AudioPreprocessor,
    VADProcessor,
    SpeakerDiarizer
)
from audtecheq.core.config import PreprocessingConfig, VADConfig, DiarizationConfig


def example_programmatic_usage():
    """
    Example of using AudioTechEquity components programmatically.
    """
    print("=== AudioTechEquity Programmatic Usage Example ===\n")
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Example audio file path (replace with your actual file)
    audio_file = Path("example_audio.wav")
    output_dir = Path("output_results")
    output_dir.mkdir(exist_ok=True)
    
    if not audio_file.exists():
        print(f"⚠️  Audio file not found: {audio_file}")
        print("Please provide a valid audio file path to run this example.")
        return
    
    try:
        # 1. Audio Preprocessing
        print("1. Audio Preprocessing")
        print("-" * 30)
        
        preprocess_config = PreprocessingConfig(
            target_sample_rate=16000,
            target_channels=1,
            target_dbfs=-20.0,
            noise_reduction_prop=0.8
        )
        
        preprocessor = AudioPreprocessor(config=preprocess_config)
        
        preprocessed_file = output_dir / "preprocessed_audio.wav"
        preprocess_result = preprocessor.process(audio_file, preprocessed_file)
        
        print(f"✅ Preprocessing completed")
        print(f"   Input: {preprocess_result['input_file']}")
        print(f"   Output: {preprocess_result['output_file']}")
        print(f"   Steps: {preprocess_result['steps_completed']}")
        print()
        
        # 2. Voice Activity Detection
        print("2. Voice Activity Detection")
        print("-" * 30)
        
        vad_config = VADConfig(
            threshold=0.5,
            min_speech_duration_ms=250,
            min_silence_duration_ms=100
        )
        
        vad_processor = VADProcessor(config=vad_config)
        
        vad_file = output_dir / "vad_processed.wav"
        vad_result = vad_processor.process(preprocessed_file, vad_file)
        
        print(f"✅ VAD completed")
        print(f"   Speech segments: {vad_result['num_segments']}")
        print(f"   Original duration: {vad_result['original_duration']:.2f}s")
        print(f"   Processed duration: {vad_result['processed_duration']:.2f}s")
        print(f"   Compression ratio: {vad_result['processing_stats']['compression_ratio']:.2f}")
        print()
        
        # 3. Speaker Diarization
        print("3. Speaker Diarization")
        print("-" * 30)
        
        diarization_config = DiarizationConfig(
            oracle_num_speakers=None,  # Let the system determine
            max_num_speakers=4,
            clustering_backend="spectral"
        )
        
        diarizer = SpeakerDiarizer(config=diarization_config)
        
        diarization_dir = output_dir / "diarization"
        diarization_result = diarizer.process(vad_file, diarization_dir)
        
        print(f"✅ Diarization completed")
        print(f"   Speakers detected: {diarization_result['num_speakers']}")
        print(f"   Total segments: {diarization_result['num_segments']}")
        print(f"   Results directory: {diarization_result['output_dir']}")
        print(f"   RTTM file: {diarization_result['rttm_file']}")
        print(f"   CSV file: {diarization_result['csv_file']}")
        print()
        
        # 4. Analysis and Quality Check
        print("4. Quality Analysis")
        print("-" * 30)
        
        quality_analysis = diarizer.analyze_diarization_quality(diarization_result['segments'])
        
        print(f"   Quality score: {quality_analysis['quality_score']:.2f}")
        print(f"   Quality level: {quality_analysis['quality_level']}")
        
        if quality_analysis['issues']:
            print("   Issues detected:")
            for issue in quality_analysis['issues']:
                print(f"     - {issue}")
        
        if quality_analysis['recommendations']:
            print("   Recommendations:")
            for rec in quality_analysis['recommendations']:
                print(f"     - {rec}")
        
        print("\n✅ Programmatic example completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during processing: {e}")
        import traceback
        traceback.print_exc()


def example_cli_usage():
    """
    Example of using AudioTechEquity via command line interface.
    """
    print("\n=== AudioTechEquity CLI Usage Examples ===\n")
    
    print("1. Complete Pipeline:")
    print("   audtecheq pipeline --input audio.wav --output results/")
    print()
    
    print("2. Individual Components:")
    print("   # Preprocessing only")
    print("   audtecheq preprocess --input audio.wav --output preprocessed.wav")
    print()
    print("   # VAD only")
    print("   audtecheq vad --input preprocessed.wav --output vad_processed.wav")
    print()
    print("   # Diarization only")
    print("   audtecheq diarize --input vad_processed.wav --output diarization_results/")
    print()
    
    print("3. Batch Processing:")
    print("   # Process entire directory")
    print("   audtecheq pipeline --input audio_directory/ --output results_directory/")
    print()
    
    print("4. Custom Configuration:")
    print("   # Using custom config file")
    print("   audtecheq pipeline --config my_config.yaml --input audio.wav --output results/")
    print()
    
    print("5. Specific Parameters:")
    print("   # Preprocessing with custom settings")
    print("   audtecheq preprocess --input audio.wav --output preprocessed.wav \\")
    print("                        --sample-rate 16000 --target-dbfs -18.0 --noise-reduction 0.9")
    print()
    print("   # Diarization with known speaker count")
    print("   audtecheq diarize --input audio.wav --output results/ --oracle-speakers 2")
    print()
    
    print("6. Testing and Evaluation:")
    print("   # Test diarization quality")
    print("   audtecheq test diarization --input diarization_results/ --expected-speakers 2")
    print()
    print("   # Test ASR with keywords")
    print("   audtecheq test asr --input transcriptions/ --keywords gfta_words.txt")
    print()


def example_configuration():
    """
    Example of creating and using custom configurations.
    """
    print("\n=== AudioTechEquity Configuration Examples ===\n")
    
    # 1. Create configuration programmatically
    print("1. Creating Configuration Programmatically:")
    print("-" * 45)
    
    config = PipelineConfig()
    
    # Modify specific settings
    config.preprocessing.target_sample_rate = 22050
    config.preprocessing.noise_reduction_prop = 0.9
    config.diarization.max_num_speakers = 3
    config.device = "cuda"
    
    print(f"   Sample rate: {config.preprocessing.target_sample_rate}")
    print(f"   Noise reduction: {config.preprocessing.noise_reduction_prop}")
    print(f"   Max speakers: {config.diarization.max_num_speakers}")
    print(f"   Device: {config.device}")
    print()
    
    # 2. Save configuration to file
    print("2. Saving Configuration to YAML:")
    print("-" * 35)
    
    config_file = Path("my_custom_config.yaml")
    config.save_yaml(config_file)
    print(f"   Configuration saved to: {config_file}")
    print()
    
    # 3. Load configuration from file
    print("3. Loading Configuration from YAML:")
    print("-" * 37)
    
    loaded_config = PipelineConfig.from_yaml(config_file)
    print(f"   Loaded sample rate: {loaded_config.preprocessing.target_sample_rate}")
    print(f"   Loaded device: {loaded_config.device}")
    print()
    
    # 4. Show configuration summary
    print("4. Configuration Summary:")
    print("-" * 26)
    
    config_dict = config.to_dict()
    for section, settings in config_dict.items():
        if isinstance(settings, dict):
            print(f"   {section}:")
            for key, value in settings.items():
                print(f"     {key}: {value}")
        else:
            print(f"   {section}: {settings}")
    print()


def main():
    """
    Main function to run all examples.
    """
    print("AudioTechEquity Usage Examples")
    print("=" * 50)
    
    # Show CLI usage examples (always available)
    example_cli_usage()
    
    # Show configuration examples
    example_configuration()
    
    # Try programmatic usage (requires audio file)
    try:
        example_programmatic_usage()
    except ImportError as e:
        print(f"\n⚠️  Some dependencies not available: {e}")
        print("Install all dependencies to run the programmatic example.")
    except Exception as e:
        print(f"\n⚠️  Could not run programmatic example: {e}")
        print("Make sure you have a valid audio file to test with.")


if __name__ == "__main__":
    main()
