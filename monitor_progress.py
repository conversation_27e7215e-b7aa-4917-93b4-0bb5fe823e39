#!/usr/bin/env python3
"""
Monitor preprocessing progress by checking the log file and output directory.
"""

import time
from pathlib import Path
import re

def monitor_progress():
    """Monitor the preprocessing progress."""
    
    # Find the most recent log file
    log_files = list(Path('.').glob('preprocessing_log_*.log'))
    if not log_files:
        print("No log files found")
        return
    
    latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
    print(f"Monitoring log file: {latest_log}")
    
    # Check output directory
    output_dir = Path('audtecheq/data/derivative/preprocessed')
    
    while True:
        try:
            # Read log file
            with open(latest_log, 'r') as f:
                content = f.read()
            
            # Extract progress information
            processing_lines = re.findall(r'Processing file (\d+)/(\d+):', content)
            if processing_lines:
                current, total = processing_lines[-1]
                print(f"Progress: {current}/{total} files processed")
            
            # Count output files
            if output_dir.exists():
                output_files = list(output_dir.rglob('*.wav'))
                print(f"Output files created: {len(output_files)}")
            
            # Check if completed
            if "Preprocessing completed!" in content:
                print("Preprocessing completed!")
                break
                
            time.sleep(10)  # Check every 10 seconds
            
        except KeyboardInterrupt:
            print("\nMonitoring stopped")
            break
        except Exception as e:
            print(f"Error: {e}")
            time.sleep(10)

if __name__ == '__main__':
    monitor_progress()
