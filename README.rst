==============
AudioTechEquity
==============

**A comprehensive audio processing pipeline for speech analysis and speaker diarization**

.. image:: https://github.com/PedzSTARlab/audtecheq/actions/workflows/docs.yml/badge.svg
        :target: https://github.com/PedzSTARlab/audtecheq/actions/workflows/docs.yml

.. image:: https://img.shields.io/pypi/v/audtecheq.svg
        :target: https://pypi.python.org/pypi/audtecheq

.. image:: https://img.shields.io/docker/pulls/PedzSTARlab/audtecheq
    :alt: Dockerpulls
    :target: https://cloud.docker.com/u/PedzSTARlab/repository/docker/PedzSTARlab/audtecheq

.. image:: https://img.shields.io/github/repo-size/PedzSTARlab/audtecheq.svg
        :target: https://img.shields.io/github/repo-size/PedzSTARlab/audtecheq.zip

.. image:: https://img.shields.io/github/issues/PedzSTARlab/audtecheq.svg
        :target: https://img.shields.io/github/issues/PedzSTARlab/audtecheq/issues

.. image:: https://img.shields.io/github/issues-pr/PedzSTARlab/audtecheq.svg
        :target: https://img.shields.io/github/issues-pr/PedzSTARlab/audtecheq/pulls

.. image:: https://img.shields.io/github/license/PedzSTARlab/audtecheq.svg
        :target: https://github.com/PedzSTARlab/audtecheq

AudioTechEquity is a modular audio processing pipeline designed for speech analysis, speaker diarization,
and automatic speech recognition. It provides both command-line tools and programmatic APIs for processing
audio files through a complete pipeline from raw audio to transcribed, speaker-segmented output.

Features
========

🎵 **Audio Preprocessing**
   - Resampling to target sample rates
   - Loudness normalization
   - Noise reduction using spectral gating

🗣️ **Voice Activity Detection (VAD)**
   - Silero VAD integration
   - Configurable speech/silence thresholds
   - Long silence removal

👥 **Speaker Diarization**
   - NeMo-based clustering diarization
   - Multiple clustering algorithms
   - Quality assessment and recommendations

🎯 **Segmentation & Processing**
   - Speaker-specific segment extraction
   - Configurable padding and crossfading
   - Chunk splitting for optimal processing

🎤 **Automatic Speech Recognition**
   - OpenAI Whisper integration
   - Multiple model sizes and languages
   - Confidence scoring and keyword analysis

🧪 **Testing & Evaluation**
   - Diarization quality metrics
   - ASR accuracy assessment
   - Keyword recognition testing

Pipeline Overview
================

.. code-block::

    Raw Audio → Preprocessing → VAD → Diarization → Segmentation → ASR → Testing
        ↓           ↓           ↓        ↓            ↓          ↓       ↓
    Audio Files  Resampled   Speech   Speaker     Segments   Transcripts Results
                Normalized  Segments  Labels      + Metadata  + Confidence Scores
                Denoised

Installation
============

Basic Installation
------------------

.. code-block:: bash

    git clone https://github.com/PedzSTARlab/audtecheq.git
    cd audtecheq
    pip install -e .

With Optional Dependencies
-------------------------

For full functionality including NeMo diarization:

.. code-block:: bash

    # Install NeMo (optional but recommended)
    pip install nemo_toolkit[asr]

    # Install additional dependencies
    pip install -r requirements.txt

Quick Start
===========

Command Line Interface
----------------------

**Complete Pipeline**

.. code-block:: bash

    # Process single audio file
    audtecheq pipeline --input audio.wav --output results/

    # Process entire directory
    audtecheq pipeline --input audio_directory/ --output results_directory/

    # With custom configuration
    audtecheq pipeline --config my_config.yaml --input audio.wav --output results/

**Individual Components**

.. code-block:: bash

    # Audio preprocessing only
    audtecheq preprocess --input audio.wav --output preprocessed.wav \
                        --sample-rate 16000 --target-dbfs -20.0

    # Voice Activity Detection
    audtecheq vad --input preprocessed.wav --output vad_processed.wav \
                  --threshold 0.5 --min-speech-duration 250

    # Speaker diarization
    audtecheq diarize --input vad_processed.wav --output diarization_results/ \
                      --oracle-speakers 2 --max-speakers 4

Programmatic Usage
------------------

**Basic Usage**

.. code-block:: python

    from audtecheq import AudioPreprocessor, SpeakerDiarizer, PipelineConfig

    # Load configuration
    config = PipelineConfig.from_yaml("config.yaml")

    # Preprocess audio
    preprocessor = AudioPreprocessor(config=config.preprocessing)
    preprocess_result = preprocessor.process("input.wav", "preprocessed.wav")

    # Perform diarization
    diarizer = SpeakerDiarizer(config=config.diarization)
    diarization_result = diarizer.process("preprocessed.wav", "diarization_output/")

    print(f"Detected {diarization_result['num_speakers']} speakers")

**Advanced Configuration**

.. code-block:: python

    from audtecheq.core.config import PreprocessingConfig, DiarizationConfig

    # Custom preprocessing configuration
    preprocess_config = PreprocessingConfig(
        target_sample_rate=22050,
        target_channels=1,
        target_dbfs=-18.0,
        noise_reduction_prop=0.9
    )

    # Custom diarization configuration
    diarization_config = DiarizationConfig(
        oracle_num_speakers=2,
        max_num_speakers=4,
        clustering_backend="spectral"
    )

    # Use with processors
    preprocessor = AudioPreprocessor(config=preprocess_config)
    diarizer = SpeakerDiarizer(config=diarization_config)

**Batch Processing**

.. code-block:: python

    from pathlib import Path
    from audtecheq import AudioPreprocessor

    preprocessor = AudioPreprocessor()

    # Process all audio files in a directory
    input_dir = Path("audio_files/")
    output_dir = Path("processed_audio/")

    results = preprocessor.process_batch(input_dir, output_dir)

    print(f"Processed {len(results['processed_files'])} files")
    print(f"Errors: {len(results['errors'])}")

Configuration
=============

YAML Configuration File
-----------------------

Create a ``config.yaml`` file to customize pipeline behavior:

.. code-block:: yaml

    # Global settings
    device: "auto"  # auto, cpu, cuda
    log_level: "INFO"

    # Preprocessing settings
    preprocessing:
      target_sample_rate: 16000
      target_channels: 1
      target_dbfs: -20.0
      noise_reduction_prop: 0.8

    # VAD settings
    vad:
      threshold: 0.5
      min_speech_duration_ms: 250
      min_silence_duration_ms: 100

    # Diarization settings
    diarization:
      oracle_num_speakers: null
      max_num_speakers: 8
      clustering_backend: "spectral"

    # ASR settings
    asr:
      model_name: "turbo"
      language: "en"
      fp16: true

Programmatic Configuration
-------------------------

.. code-block:: python

    from audtecheq import PipelineConfig

    # Create default configuration
    config = PipelineConfig()

    # Modify settings
    config.preprocessing.target_sample_rate = 22050
    config.diarization.max_num_speakers = 3
    config.device = "cuda"

    # Save to file
    config.save_yaml("my_config.yaml")

    # Load from file
    loaded_config = PipelineConfig.from_yaml("my_config.yaml")

Components
==========

Audio Preprocessing
------------------

The preprocessing module handles audio format standardization:

.. code-block:: python

    from audtecheq.preprocessing import AudioResampler, AudioNormalizer, AudioDenoiser

    # Individual components
    resampler = AudioResampler(target_sample_rate=16000, target_channels=1)
    normalizer = AudioNormalizer(target_dbfs=-20.0)
    denoiser = AudioDenoiser(prop_decrease=0.8)

    # Process step by step
    resampler.process("input.wav", "resampled.wav")
    normalizer.process("resampled.wav", "normalized.wav")
    denoiser.process("normalized.wav", "denoised.wav")

Voice Activity Detection
-----------------------

Remove silence and detect speech segments:

.. code-block:: python

    from audtecheq.vad import VADProcessor

    vad = VADProcessor()
    result = vad.process("audio.wav", "speech_only.wav")

    print(f"Original duration: {result['original_duration']:.2f}s")
    print(f"Speech duration: {result['processed_duration']:.2f}s")
    print(f"Speech segments: {result['num_segments']}")

Speaker Diarization
------------------

Identify and segment speakers:

.. code-block:: python

    from audtecheq.diarization import SpeakerDiarizer

    diarizer = SpeakerDiarizer()
    result = diarizer.process("audio.wav", "diarization_output/")

    print(f"Speakers detected: {result['num_speakers']}")
    print(f"Segments: {result['num_segments']}")

    # Analyze quality
    quality = diarizer.analyze_diarization_quality(result['segments'])
    print(f"Quality score: {quality['quality_score']:.2f}")

Testing and Evaluation
======================

Diarization Quality Assessment
-----------------------------

.. code-block:: bash

    # Test diarization results
    audtecheq test diarization --input diarization_results/ \
                              --expected-speakers 2 \
                              --output quality_report.json

ASR and Keyword Testing
----------------------

.. code-block:: bash

    # Test ASR quality and keyword recognition
    audtecheq test asr --input transcriptions/ \
                       --keywords gfta_words.txt \
                       --output asr_report.json

Advanced Usage
==============

Custom Processors
----------------

Create custom processors by inheriting from ``BaseProcessor``:

.. code-block:: python

    from audtecheq.core import BaseProcessor
    from pathlib import Path

    class CustomProcessor(BaseProcessor):
        def process(self, input_path, output_path, **kwargs):
            input_path = self.validate_input_path(input_path)
            output_path = Path(output_path)
            self.ensure_output_dir(output_path.parent)

            # Your custom processing logic here

            return {
                "input_file": str(input_path),
                "output_file": str(output_path),
                "success": True
            }

Error Handling
-------------

.. code-block:: python

    from audtecheq.core.exceptions import ProcessingError, ValidationError

    try:
        result = preprocessor.process("input.wav", "output.wav")
    except ValidationError as e:
        print(f"Input validation failed: {e}")
    except ProcessingError as e:
        print(f"Processing failed: {e}")

Troubleshooting
===============

Common Issues
------------

**NeMo Installation Issues**

If you encounter issues with NeMo installation:

.. code-block:: bash

    # Install NeMo dependencies first
    pip install cython
    pip install nemo_toolkit[asr] --no-deps
    pip install -r requirements.txt

**CUDA/GPU Issues**

For GPU acceleration:

.. code-block:: bash

    # Check CUDA availability
    python -c "import torch; print(torch.cuda.is_available())"

    # Force CPU usage if needed
    audtecheq pipeline --device cpu --input audio.wav --output results/

**Memory Issues**

For large files or limited memory:

.. code-block:: bash

    # Process with smaller batch size
    audtecheq pipeline --batch-size 1 --input large_audio.wav --output results/

Contributing
============

We welcome contributions! Please see our contributing guidelines for details.

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

License
=======

This project is licensed under the MIT License - see the LICENSE file for details.

Citation
========

If you use AudioTechEquity in your research, please cite:

.. code-block:: bibtex

    @software{audiotechequity2024,
      title={AudioTechEquity: A Modular Audio Processing Pipeline},
      author={PedzSTARlab},
      year={2024},
      url={https://github.com/PedzSTARlab/audtecheq}
    }