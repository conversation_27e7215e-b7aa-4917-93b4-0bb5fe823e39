#!/usr/bin/env python3
"""
Simplified batch processing script for AudioTechEquity participants.

This script processes audio files through basic preprocessing and testing
without requiring all the complex dependencies.
"""

import argparse
import json
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List

# Add current directory to path for imports
sys.path.insert(0, '.')


def setup_logging(log_level: str = "INFO") -> None:
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'simple_batch_processing_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        ]
    )


def discover_participants(data_dir: Path) -> Dict[str, List[Path]]:
    """
    Discover all participants and their audio files.
    
    Parameters
    ----------
    data_dir : Path
        Base data directory
        
    Returns
    -------
    dict
        Dictionary mapping participant IDs to lists of audio files
    """
    participants = {}
    
    for participant_dir in data_dir.glob("sub-ATL*"):
        if participant_dir.is_dir():
            participant_id = participant_dir.name
            audio_files = []
            
            # Find all WAV files in behavioral data
            beh_dir = participant_dir / "beh"
            if beh_dir.exists():
                audio_files.extend(list(beh_dir.glob("*.wav")))
            
            if audio_files:
                participants[participant_id] = sorted(audio_files)
    
    return participants


def process_single_file(audio_file: Path, output_base: Path, logger) -> Dict:
    """
    Process a single audio file with basic analysis.
    
    Parameters
    ----------
    audio_file : Path
        Path to audio file
    output_base : Path
        Base output directory
    logger : logging.Logger
        Logger instance
        
    Returns
    -------
    dict
        Processing results
    """
    file_stem = audio_file.stem
    logger.info(f"🎵 Processing: {file_stem}")
    
    results = {
        "input_file": str(audio_file),
        "file_stem": file_stem,
        "success": False,
        "steps_completed": [],
        "errors": [],
        "file_info": {}
    }
    
    try:
        # Basic file analysis
        logger.info(f"  📊 Analyzing file properties")
        
        # Get file size
        file_size = audio_file.stat().st_size
        results["file_info"]["size_bytes"] = file_size
        results["file_info"]["size_mb"] = round(file_size / (1024 * 1024), 2)
        
        # Try to get audio properties using pydub
        try:
            from pydub import AudioSegment
            audio = AudioSegment.from_file(str(audio_file))
            
            results["file_info"]["duration_seconds"] = len(audio) / 1000.0
            results["file_info"]["duration_minutes"] = round(len(audio) / 60000.0, 2)
            results["file_info"]["sample_rate"] = audio.frame_rate
            results["file_info"]["channels"] = audio.channels
            results["file_info"]["sample_width"] = audio.sample_width
            results["file_info"]["frame_count"] = audio.frame_count()
            
            # Basic audio quality metrics
            results["file_info"]["max_dBFS"] = audio.max_dBFS
            results["file_info"]["rms_dBFS"] = audio.dBFS
            
            logger.info(f"    Duration: {results['file_info']['duration_minutes']:.2f} minutes")
            logger.info(f"    Sample rate: {results['file_info']['sample_rate']} Hz")
            logger.info(f"    Channels: {results['file_info']['channels']}")
            logger.info(f"    RMS dBFS: {results['file_info']['rms_dBFS']:.2f}")
            
            results["steps_completed"].append("audio_analysis")
            
        except Exception as e:
            logger.warning(f"    Could not analyze audio properties: {e}")
            results["errors"].append(f"Audio analysis failed: {str(e)}")
        
        # Create organized output structure
        participant_output = output_base / "analysis"
        participant_output.mkdir(parents=True, exist_ok=True)
        
        # Save individual file analysis
        file_analysis_path = participant_output / f"{file_stem}_analysis.json"
        with open(file_analysis_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        results["analysis_file"] = str(file_analysis_path)
        results["steps_completed"].append("save_analysis")
        
        results["success"] = True
        logger.info(f"  ✅ Completed: {file_stem}")
        
    except Exception as e:
        error_msg = f"Processing failed for {file_stem}: {str(e)}"
        logger.error(f"  ❌ {error_msg}")
        results["errors"].append(error_msg)
    
    return results


def process_participant(participant_id: str, audio_files: List[Path], 
                       output_dir: Path, logger) -> Dict:
    """
    Process all audio files for a single participant.
    
    Parameters
    ----------
    participant_id : str
        Participant identifier
    audio_files : list
        List of audio files for this participant
    output_dir : Path
        Output directory
    logger : logging.Logger
        Logger instance
        
    Returns
    -------
    dict
        Participant processing results
    """
    logger.info(f"🧑 Processing participant: {participant_id}")
    logger.info(f"   Files to process: {len(audio_files)}")
    
    participant_output = output_dir / participant_id
    participant_output.mkdir(parents=True, exist_ok=True)
    
    participant_results = {
        "participant_id": participant_id,
        "total_files": len(audio_files),
        "successful_files": 0,
        "failed_files": 0,
        "file_results": [],
        "summary": {}
    }
    
    total_duration = 0
    total_size = 0
    sample_rates = []
    
    for audio_file in audio_files:
        file_result = process_single_file(
            audio_file, participant_output, logger
        )
        participant_results["file_results"].append(file_result)
        
        if file_result["success"]:
            participant_results["successful_files"] += 1
            
            # Aggregate statistics
            if "file_info" in file_result:
                info = file_result["file_info"]
                if "duration_seconds" in info:
                    total_duration += info["duration_seconds"]
                if "size_bytes" in info:
                    total_size += info["size_bytes"]
                if "sample_rate" in info:
                    sample_rates.append(info["sample_rate"])
        else:
            participant_results["failed_files"] += 1
    
    # Generate participant summary
    participant_results["summary"] = {
        "success_rate": participant_results["successful_files"] / participant_results["total_files"],
        "total_duration_minutes": round(total_duration / 60, 2),
        "total_size_mb": round(total_size / (1024 * 1024), 2),
        "unique_sample_rates": list(set(sample_rates)),
        "average_file_duration_minutes": round((total_duration / 60) / len(audio_files), 2) if audio_files else 0
    }
    
    # Save participant summary
    summary_path = participant_output / f"{participant_id}_summary.json"
    with open(summary_path, 'w') as f:
        json.dump(participant_results, f, indent=2, default=str)
    
    logger.info(f"   ✅ Participant {participant_id} completed:")
    logger.info(f"      Successful: {participant_results['successful_files']}/{participant_results['total_files']}")
    logger.info(f"      Success rate: {participant_results['summary']['success_rate']:.1%}")
    logger.info(f"      Total duration: {participant_results['summary']['total_duration_minutes']:.1f} minutes")
    logger.info(f"      Total size: {participant_results['summary']['total_size_mb']:.1f} MB")
    
    return participant_results


def generate_batch_report(all_results: List[Dict], output_dir: Path, logger) -> None:
    """Generate comprehensive batch processing report."""
    logger.info("📊 Generating batch processing report...")
    
    # Calculate overall statistics
    total_participants = len(all_results)
    total_files = sum(r["total_files"] for r in all_results)
    total_successful = sum(r["successful_files"] for r in all_results)
    total_failed = sum(r["failed_files"] for r in all_results)
    
    overall_success_rate = total_successful / total_files if total_files > 0 else 0
    total_duration = sum(r["summary"]["total_duration_minutes"] for r in all_results)
    total_size = sum(r["summary"]["total_size_mb"] for r in all_results)
    
    # Create summary report
    report = {
        "batch_processing_summary": {
            "timestamp": datetime.now().isoformat(),
            "total_participants": total_participants,
            "total_files": total_files,
            "successful_files": total_successful,
            "failed_files": total_failed,
            "overall_success_rate": overall_success_rate,
            "total_duration_minutes": total_duration,
            "total_duration_hours": round(total_duration / 60, 2),
            "total_size_mb": total_size,
            "total_size_gb": round(total_size / 1024, 2),
            "average_files_per_participant": round(total_files / total_participants, 1) if total_participants > 0 else 0
        },
        "participant_results": all_results,
        "processing_statistics": {
            "participants_with_100_percent_success": sum(1 for r in all_results if r["summary"]["success_rate"] == 1.0),
            "participants_with_partial_success": sum(1 for r in all_results if 0 < r["summary"]["success_rate"] < 1.0),
            "participants_with_no_success": sum(1 for r in all_results if r["summary"]["success_rate"] == 0.0)
        }
    }
    
    # Save report
    report_path = output_dir / f"batch_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    logger.info(f"📋 Batch processing report saved: {report_path}")
    logger.info(f"📈 Overall Results:")
    logger.info(f"   Participants: {total_participants}")
    logger.info(f"   Total files: {total_files}")
    logger.info(f"   Success rate: {overall_success_rate:.1%}")
    logger.info(f"   Total audio duration: {total_duration:.1f} minutes ({total_duration/60:.1f} hours)")
    logger.info(f"   Total data size: {total_size:.1f} MB ({total_size/1024:.1f} GB)")


def main():
    """Main batch processing function."""
    parser = argparse.ArgumentParser(
        description="Simple batch analysis of AudioTechEquity participants",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        '--data-dir',
        type=Path,
        default=Path('./audtecheq/data'),
        help='Input data directory containing participants'
    )
    
    parser.add_argument(
        '--output-dir',
        type=Path,
        default=Path('./data_analysis'),
        help='Output directory for analysis results'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level'
    )
    
    parser.add_argument(
        '--participants',
        nargs='+',
        help='Specific participants to process (default: all)'
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 Starting AudioTechEquity simple batch analysis")
    logger.info(f"📁 Data directory: {args.data_dir}")
    logger.info(f"📁 Output directory: {args.output_dir}")
    
    # Create output directory
    args.output_dir.mkdir(parents=True, exist_ok=True)
    
    # Discover participants
    participants = discover_participants(args.data_dir)
    
    if not participants:
        logger.error("❌ No participants found in data directory")
        return 1
    
    # Filter participants if specified
    if args.participants:
        participants = {pid: files for pid, files in participants.items() 
                      if pid in args.participants}
    
    logger.info(f"👥 Found {len(participants)} participants:")
    for pid, files in participants.items():
        logger.info(f"   {pid}: {len(files)} files")
    
    # Process all participants
    all_results = []
    
    for participant_id, audio_files in participants.items():
        participant_result = process_participant(
            participant_id, audio_files, args.output_dir, logger
        )
        all_results.append(participant_result)
    
    # Generate comprehensive report
    generate_batch_report(all_results, args.output_dir, logger)
    
    logger.info("🎉 Batch analysis completed successfully!")
    
    return 0


if __name__ == "__main__":
    exit(main())
