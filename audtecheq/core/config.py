"""
Configuration management for AudioTechEquity pipeline.
"""

import os
import yaml
from typing import Any, Dict, Optional, Union
from pathlib import Path
from dataclasses import dataclass, field

from .exceptions import ConfigurationError


@dataclass
class PreprocessingConfig:
    """Configuration for audio preprocessing."""
    target_sample_rate: int = 16000
    target_channels: int = 1
    target_dbfs: float = -20.0
    noise_reduction_prop: float = 0.8
    noise_reduction_stationary: bool = True


@dataclass
class VADConfig:
    """Configuration for Voice Activity Detection."""
    model_name: str = "silero_vad"
    threshold: float = 0.5
    min_speech_duration_ms: int = 250
    min_silence_duration_ms: int = 100
    window_size_samples: int = 1536
    speech_pad_ms: int = 30


@dataclass
class DiarizationConfig:
    """Configuration for speaker diarization."""
    model_name: str = "nvidia/speakerverification_en_titanet_large"
    clustering_backend: str = "spectral"
    oracle_num_speakers: Optional[int] = None
    max_num_speakers: int = 8
    enhanced_count_thres: float = 0.8
    sparse_search_volume: int = 30


@dataclass
class SegmentationConfig:
    """Configuration for audio segmentation."""
    padding_sec: float = 0.2
    apply_crossfade: bool = False
    crossfade_sec: float = 0.1
    noise_amplitude: float = 0.005
    max_segment_length: float = 15.0
    min_segment_length: float = 0.75


@dataclass
class ASRConfig:
    """Configuration for Automatic Speech Recognition."""
    model_name: str = "turbo"
    language: str = "en"
    fp16: bool = True
    temperature: float = 0.0
    best_of: int = 5
    beam_size: int = 5


@dataclass
class KeywordConfig:
    """Configuration for keyword recognition and testing."""
    gfta_words: list = field(default_factory=lambda: [
        "House", "Door", "Pig", "Cup", "Boy", "Apple", "Go", "Duck", "Quack", "Table",
        "Monkey", "Hammer", "Fish", "Watch", "Spider", "Web", "Drum", "Plate", "Knife",
        "Shoe", "Slide", "Swing", "Guitar", "Lion", "Chair", "Soap", "Glasses", "Tiger",
        "Puzzle", "Finger", "Ring", "Thumb", "Elephant", "Vacuum", "Shovel", "Teacher",
        "Zebra", "Giraffe", "Vegetable", "Brushing", "Blue", "Yellow", "Brother", "Frog",
        "Green", "That", "Leaf", "Cookie", "Cheese", "Pajamas", "Teeth", "Princess", "Crown"
    ])
    word_repetition_task: list = field(default_factory=lambda: [
        "cash", "boat", "duck", "shoot", "big", "bed", "sheep", "dog", "cage", "cat", "chop"
    ])
    fuzzy_match_threshold: float = 0.8
    edit_distance_threshold: int = 2


@dataclass
class PipelineConfig:
    """Main configuration class for the entire pipeline."""
    preprocessing: PreprocessingConfig = field(default_factory=PreprocessingConfig)
    vad: VADConfig = field(default_factory=VADConfig)
    diarization: DiarizationConfig = field(default_factory=DiarizationConfig)
    segmentation: SegmentationConfig = field(default_factory=SegmentationConfig)
    asr: ASRConfig = field(default_factory=ASRConfig)
    keywords: KeywordConfig = field(default_factory=KeywordConfig)
    
    # Global settings
    device: str = "auto"  # auto, cpu, cuda
    num_workers: int = 1
    batch_size: int = 1
    output_format: str = "wav"
    log_level: str = "INFO"
    
    @classmethod
    def from_yaml(cls, config_path: Union[str, Path]) -> 'PipelineConfig':
        """
        Load configuration from YAML file.
        
        Parameters
        ----------
        config_path : str or Path
            Path to YAML configuration file
            
        Returns
        -------
        PipelineConfig
            Loaded configuration object
        """
        config_path = Path(config_path)
        if not config_path.exists():
            raise ConfigurationError(f"Configuration file not found: {config_path}")
        
        try:
            with open(config_path, 'r') as f:
                config_dict = yaml.safe_load(f)
            
            return cls.from_dict(config_dict)
        except yaml.YAMLError as e:
            raise ConfigurationError(f"Error parsing YAML configuration: {e}")
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'PipelineConfig':
        """
        Create configuration from dictionary.
        
        Parameters
        ----------
        config_dict : dict
            Configuration dictionary
            
        Returns
        -------
        PipelineConfig
            Configuration object
        """
        # Extract nested configurations
        preprocessing_config = PreprocessingConfig(**config_dict.get('preprocessing', {}))
        vad_config = VADConfig(**config_dict.get('vad', {}))
        diarization_config = DiarizationConfig(**config_dict.get('diarization', {}))
        segmentation_config = SegmentationConfig(**config_dict.get('segmentation', {}))
        asr_config = ASRConfig(**config_dict.get('asr', {}))
        keywords_config = KeywordConfig(**config_dict.get('keywords', {}))
        
        # Extract global settings
        global_settings = {k: v for k, v in config_dict.items() 
                          if k not in ['preprocessing', 'vad', 'diarization', 'segmentation', 'asr', 'keywords']}
        
        return cls(
            preprocessing=preprocessing_config,
            vad=vad_config,
            diarization=diarization_config,
            segmentation=segmentation_config,
            asr=asr_config,
            keywords=keywords_config,
            **global_settings
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert configuration to dictionary.
        
        Returns
        -------
        dict
            Configuration as dictionary
        """
        return {
            'preprocessing': self.preprocessing.__dict__,
            'vad': self.vad.__dict__,
            'diarization': self.diarization.__dict__,
            'segmentation': self.segmentation.__dict__,
            'asr': self.asr.__dict__,
            'keywords': self.keywords.__dict__,
            'device': self.device,
            'num_workers': self.num_workers,
            'batch_size': self.batch_size,
            'output_format': self.output_format,
            'log_level': self.log_level
        }
    
    def save_yaml(self, output_path: Union[str, Path]) -> None:
        """
        Save configuration to YAML file.
        
        Parameters
        ----------
        output_path : str or Path
            Output path for YAML file
        """
        output_path = Path(output_path)
        config_dict = self.to_dict()
        
        with open(output_path, 'w') as f:
            yaml.dump(config_dict, f, default_flow_style=False, indent=2)
    
    def get_device(self) -> str:
        """
        Get the appropriate device for processing.
        
        Returns
        -------
        str
            Device string ('cpu' or 'cuda')
        """
        if self.device == "auto":
            try:
                import torch
                return "cuda" if torch.cuda.is_available() else "cpu"
            except ImportError:
                return "cpu"
        return self.device
