"""
Data management utilities for AudioTechEquity.

This module provides centralized data organization and storage management
for raw data, preprocessed derivatives, and test results.
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional, Union

from .exceptions import ProcessingError


class DataManager:
    """
    Centralized data management for AudioTechEquity pipeline.
    
    Organizes data into structured directories:
    - data/raw/: Original audio files
    - data/derivatives/: Preprocessed and processed data
    - data/results/: Test results and evaluation reports
    """
    
    def __init__(self, base_data_dir: Optional[Union[str, Path]] = None):
        """
        Initialize data manager.
        
        Parameters
        ----------
        base_data_dir : str or Path, optional
            Base directory for data storage. Defaults to 'data' in current directory.
        """
        self.logger = logging.getLogger(__name__)
        
        if base_data_dir is None:
            self.base_data_dir = Path.cwd() / "data"
        else:
            self.base_data_dir = Path(base_data_dir)
        
        # Define standard directory structure
        self.raw_dir = self.base_data_dir / "raw"
        self.derivatives_dir = self.base_data_dir / "derivatives"
        self.results_dir = self.base_data_dir / "results"
        
        # Create directory structure
        self._create_directory_structure()
    
    def _create_directory_structure(self) -> None:
        """Create the standard data directory structure."""
        directories = [
            self.raw_dir,
            self.derivatives_dir,
            self.derivatives_dir / "preprocessed",
            self.derivatives_dir / "vad",
            self.derivatives_dir / "silence_processed",
            self.derivatives_dir / "diarization",
            self.derivatives_dir / "segmentation",
            self.derivatives_dir / "asr",
            self.results_dir,
            self.results_dir / "test_reports",
            self.results_dir / "evaluations",
            self.results_dir / "metrics"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            
        # Create README files for documentation
        self._create_readme_files()
    
    def _create_readme_files(self) -> None:
        """Create README files explaining directory structure."""
        readme_content = {
            self.raw_dir / "README.md": """# Raw Data Directory

This directory contains original, unprocessed audio files.

## Organization
- Organize files by project, session, or participant as needed
- Maintain original filenames when possible
- Include metadata files (JSON, CSV) alongside audio files

## Supported Formats
- WAV (recommended)
- MP3, FLAC, M4A (will be converted during preprocessing)
""",
            
            self.derivatives_dir / "README.md": """# Derivatives Directory

This directory contains processed and derived data from the AudioTechEquity pipeline.

## Subdirectories
- `preprocessed/`: Resampled, normalized, and denoised audio
- `vad/`: Voice activity detection results
- `silence_processed/`: Audio with long silences removed
- `diarization/`: Speaker diarization results (RTTM, CSV)
- `segmentation/`: Speaker-segmented audio files
- `asr/`: Automatic speech recognition transcripts

## File Naming Convention
Files maintain relationship to original raw data through consistent naming.
""",
            
            self.results_dir / "README.md": """# Results Directory

This directory contains test results, evaluations, and metrics.

## Subdirectories
- `test_reports/`: Detailed test reports (JSON format)
- `evaluations/`: Component-specific evaluation results
- `metrics/`: Aggregated metrics and statistics

## File Types
- JSON: Structured test results and metrics
- CSV: Tabular data for analysis
- HTML: Human-readable reports (future)
"""
        }
        
        for readme_path, content in readme_content.items():
            if not readme_path.exists():
                with open(readme_path, 'w') as f:
                    f.write(content)
    
    def get_raw_path(self, filename: str) -> Path:
        """Get path for raw data file."""
        return self.raw_dir / filename
    
    def get_derivatives_path(self, step: str, filename: str) -> Path:
        """
        Get path for derivative data file.
        
        Parameters
        ----------
        step : str
            Processing step (preprocessed, vad, diarization, etc.)
        filename : str
            Filename
            
        Returns
        -------
        Path
            Full path to derivative file
        """
        step_dir = self.derivatives_dir / step
        step_dir.mkdir(exist_ok=True)
        return step_dir / filename
    
    def get_results_path(self, category: str, filename: str) -> Path:
        """
        Get path for results file.
        
        Parameters
        ----------
        category : str
            Results category (test_reports, evaluations, metrics)
        filename : str
            Filename
            
        Returns
        -------
        Path
            Full path to results file
        """
        category_dir = self.results_dir / category
        category_dir.mkdir(exist_ok=True)
        return category_dir / filename
    
    def save_test_results(self, results: Dict[str, Any], test_type: str, 
                         filename: Optional[str] = None) -> Path:
        """
        Save test results with standardized naming and metadata.
        
        Parameters
        ----------
        results : dict
            Test results data
        test_type : str
            Type of test (diarization, asr, pipeline, etc.)
        filename : str, optional
            Custom filename. If None, generates timestamp-based name.
            
        Returns
        -------
        Path
            Path to saved results file
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{test_type}_test_{timestamp}.json"
        
        if not filename.endswith('.json'):
            filename += '.json'
        
        output_path = self.get_results_path("test_reports", filename)
        
        # Add standardized metadata
        enhanced_results = {
            **results,
            "metadata": {
                **results.get("metadata", {}),
                "test_type": test_type,
                "saved_timestamp": datetime.now().isoformat(),
                "data_manager_version": "1.0",
                "file_path": str(output_path)
            }
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(enhanced_results, f, indent=2, ensure_ascii=False, default=str)
        
        self.logger.info(f"Test results saved: {output_path}")
        return output_path
    
    def save_processing_metadata(self, metadata: Dict[str, Any], step: str, 
                               filename: str) -> Path:
        """
        Save processing metadata alongside derivative data.
        
        Parameters
        ----------
        metadata : dict
            Processing metadata
        step : str
            Processing step
        filename : str
            Base filename (will add _metadata.json)
            
        Returns
        -------
        Path
            Path to saved metadata file
        """
        base_name = Path(filename).stem
        metadata_filename = f"{base_name}_metadata.json"
        metadata_path = self.get_derivatives_path(step, metadata_filename)
        
        # Add standardized metadata
        enhanced_metadata = {
            **metadata,
            "processing_step": step,
            "original_filename": filename,
            "saved_timestamp": datetime.now().isoformat(),
            "data_manager_version": "1.0"
        }
        
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(enhanced_metadata, f, indent=2, ensure_ascii=False, default=str)
        
        return metadata_path
    
    def get_pipeline_session_dir(self, session_id: str) -> Path:
        """
        Get directory for a complete pipeline session.
        
        Parameters
        ----------
        session_id : str
            Unique session identifier
            
        Returns
        -------
        Path
            Session directory path
        """
        session_dir = self.derivatives_dir / "sessions" / session_id
        session_dir.mkdir(parents=True, exist_ok=True)
        return session_dir
    
    def organize_pipeline_outputs(self, session_id: str, pipeline_outputs: Dict[str, Path]) -> Dict[str, Path]:
        """
        Organize pipeline outputs into structured directories.
        
        Parameters
        ----------
        session_id : str
            Pipeline session identifier
        pipeline_outputs : dict
            Dictionary mapping step names to output paths
            
        Returns
        -------
        dict
            Dictionary mapping step names to organized paths
        """
        session_dir = self.get_pipeline_session_dir(session_id)
        organized_paths = {}
        
        step_mapping = {
            "preprocess": "preprocessed",
            "vad": "vad", 
            "discard-silences": "silence_processed",
            "diarize": "diarization",
            "segment": "segmentation",
            "asr": "asr"
        }
        
        for step, output_path in pipeline_outputs.items():
            if step in step_mapping:
                target_step = step_mapping[step]
                target_dir = session_dir / target_step
                target_dir.mkdir(exist_ok=True)
                
                # Copy or move files to organized location
                if Path(output_path).is_file():
                    target_path = target_dir / Path(output_path).name
                    organized_paths[step] = target_path
                else:
                    organized_paths[step] = target_dir
        
        return organized_paths
    
    def cleanup_temp_files(self, temp_dirs: list) -> None:
        """
        Clean up temporary directories and files.
        
        Parameters
        ----------
        temp_dirs : list
            List of temporary directory paths to clean up
        """
        for temp_dir in temp_dirs:
            temp_path = Path(temp_dir)
            if temp_path.exists() and temp_path.is_dir():
                try:
                    # Remove all files in temp directory
                    for file_path in temp_path.rglob("*"):
                        if file_path.is_file():
                            file_path.unlink()
                    
                    # Remove empty directories
                    for dir_path in sorted(temp_path.rglob("*"), reverse=True):
                        if dir_path.is_dir() and not any(dir_path.iterdir()):
                            dir_path.rmdir()
                    
                    # Remove temp directory itself if empty
                    if not any(temp_path.iterdir()):
                        temp_path.rmdir()
                        
                    self.logger.debug(f"Cleaned up temporary directory: {temp_path}")
                    
                except Exception as e:
                    self.logger.warning(f"Failed to clean up {temp_path}: {e}")
    
    def get_directory_info(self) -> Dict[str, Any]:
        """
        Get information about the data directory structure.
        
        Returns
        -------
        dict
            Directory structure information
        """
        def count_files(directory: Path) -> int:
            if not directory.exists():
                return 0
            return sum(1 for _ in directory.rglob("*") if _.is_file())
        
        return {
            "base_directory": str(self.base_data_dir),
            "structure": {
                "raw": {
                    "path": str(self.raw_dir),
                    "file_count": count_files(self.raw_dir)
                },
                "derivatives": {
                    "path": str(self.derivatives_dir),
                    "file_count": count_files(self.derivatives_dir),
                    "subdirectories": {
                        "preprocessed": count_files(self.derivatives_dir / "preprocessed"),
                        "vad": count_files(self.derivatives_dir / "vad"),
                        "silence_processed": count_files(self.derivatives_dir / "silence_processed"),
                        "diarization": count_files(self.derivatives_dir / "diarization"),
                        "segmentation": count_files(self.derivatives_dir / "segmentation"),
                        "asr": count_files(self.derivatives_dir / "asr")
                    }
                },
                "results": {
                    "path": str(self.results_dir),
                    "file_count": count_files(self.results_dir),
                    "subdirectories": {
                        "test_reports": count_files(self.results_dir / "test_reports"),
                        "evaluations": count_files(self.results_dir / "evaluations"),
                        "metrics": count_files(self.results_dir / "metrics")
                    }
                }
            }
        }
