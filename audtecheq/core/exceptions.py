"""
Custom exceptions for AudioTechEquity pipeline.
"""


class AudioTechEquityError(Exception):
    """Base exception for AudioTechEquity pipeline."""
    pass


class ProcessingError(AudioTechEquityError):
    """Raised when audio processing fails."""
    pass


class ValidationError(AudioTechEquityError):
    """Raised when input validation fails."""
    pass


class ConfigurationError(AudioTechEquityError):
    """Raised when configuration is invalid."""
    pass


class ModelLoadError(AudioTechEquityError):
    """Raised when model loading fails."""
    pass


class DiarizationError(ProcessingError):
    """Raised when diarization processing fails."""
    pass


class ASRError(ProcessingError):
    """Raised when ASR processing fails."""
    pass


class SegmentationError(ProcessingError):
    """Raised when segmentation processing fails."""
    pass
