"""
Base classes for AudioTechEquity pipeline components.
"""

import os
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from pathlib import Path

from .exceptions import ProcessingError, ValidationError


class BaseProcessor(ABC):
    """
    Abstract base class for all audio processing components.
    
    This class provides common functionality for all processors including
    logging, validation, and standardized interfaces.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, logger: Optional[logging.Logger] = None):
        """
        Initialize the base processor.
        
        Parameters
        ----------
        config : dict, optional
            Configuration parameters for the processor
        logger : logging.Logger, optional
            Logger instance. If None, creates a default logger
        """
        self.config = config or {}
        self.logger = logger or self._setup_logger()
        self._validate_config()
    
    def _setup_logger(self) -> logging.Logger:
        """Setup default logger for the processor."""
        logger = logging.getLogger(self.__class__.__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _validate_config(self) -> None:
        """Validate processor configuration. Override in subclasses."""
        pass
    
    def validate_input_path(self, path: Union[str, Path]) -> Path:
        """
        Validate that input path exists and is accessible.
        
        Parameters
        ----------
        path : str or Path
            Path to validate
            
        Returns
        -------
        Path
            Validated path object
            
        Raises
        ------
        ValidationError
            If path doesn't exist or is not accessible
        """
        path = Path(path)
        if not path.exists():
            raise ValidationError(f"Input path does not exist: {path}")
        return path
    
    def ensure_output_dir(self, path: Union[str, Path]) -> Path:
        """
        Ensure output directory exists, create if necessary.
        
        Parameters
        ----------
        path : str or Path
            Output directory path
            
        Returns
        -------
        Path
            Validated output directory path
        """
        path = Path(path)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    @abstractmethod
    def process(self, input_path: Union[str, Path], output_path: Union[str, Path], **kwargs) -> Dict[str, Any]:
        """
        Process input and save to output path.
        
        Parameters
        ----------
        input_path : str or Path
            Path to input file or directory
        output_path : str or Path
            Path to output file or directory
        **kwargs
            Additional processing parameters
            
        Returns
        -------
        dict
            Processing results and metadata
        """
        pass
    
    def get_supported_formats(self) -> List[str]:
        """
        Get list of supported file formats.
        
        Returns
        -------
        list
            List of supported file extensions
        """
        return ['.wav', '.mp3', '.flac', '.m4a']
    
    def filter_audio_files(self, directory: Union[str, Path]) -> List[Path]:
        """
        Filter audio files from directory based on supported formats.
        
        Parameters
        ----------
        directory : str or Path
            Directory to scan for audio files
            
        Returns
        -------
        list
            List of audio file paths
        """
        directory = Path(directory)
        supported_formats = self.get_supported_formats()
        audio_files = []
        
        for file_path in directory.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in supported_formats:
                audio_files.append(file_path)
        
        return sorted(audio_files)


class BatchProcessor(BaseProcessor):
    """
    Base class for processors that handle batch operations.
    """
    
    def process_batch(self, input_dir: Union[str, Path], output_dir: Union[str, Path], **kwargs) -> Dict[str, Any]:
        """
        Process all audio files in input directory.
        
        Parameters
        ----------
        input_dir : str or Path
            Input directory containing audio files
        output_dir : str or Path
            Output directory for processed files
        **kwargs
            Additional processing parameters
            
        Returns
        -------
        dict
            Batch processing results
        """
        input_dir = self.validate_input_path(input_dir)
        output_dir = self.ensure_output_dir(output_dir)
        
        audio_files = self.filter_audio_files(input_dir)
        if not audio_files:
            self.logger.warning(f"No audio files found in {input_dir}")
            return {"processed_files": [], "errors": []}
        
        results = {"processed_files": [], "errors": []}
        
        for audio_file in audio_files:
            try:
                self.logger.info(f"Processing {audio_file.name}")
                output_file = output_dir / audio_file.name
                result = self.process(audio_file, output_file, **kwargs)
                results["processed_files"].append({
                    "input": str(audio_file),
                    "output": str(output_file),
                    "result": result
                })
            except Exception as e:
                error_msg = f"Error processing {audio_file.name}: {str(e)}"
                self.logger.error(error_msg)
                results["errors"].append({
                    "file": str(audio_file),
                    "error": error_msg
                })
        
        return results
