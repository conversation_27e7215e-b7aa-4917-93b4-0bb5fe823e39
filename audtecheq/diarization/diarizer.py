"""
Speaker diarization processor using NeMo clustering diarizer.
"""

import os
import json
import tempfile
import logging
from typing import Dict, Any, Union, Optional, List
from pathlib import Path

import torch
import torchaudio
import pandas as pd
import numpy as np
from omegaconf import OmegaConf

from ..core.base import BaseProcessor
from ..core.config import DiarizationConfig
from ..core.exceptions import ProcessingError, ModelLoadError


class SpeakerDiarizer(BaseProcessor):
    """
    Speaker diarization processor.
    
    Uses NeMo ClusteringDiarizer for speaker identification and segmentation.
    """
    
    def __init__(self, config: Optional[DiarizationConfig] = None, **kwargs):
        """
        Initialize the speaker diarizer.
        
        Parameters
        ----------
        config : DiarizationConfig, optional
            Diarization configuration
        **kwargs
            Additional arguments passed to base class
        """
        super().__init__(config=config.__dict__ if config else None, **kwargs)
        
        self.diarization_config = config or DiarizationConfig()
        self.diarizer = None
        self.device = self._get_device()
        
        # Load diarization model
        self._load_model()
    
    def _get_device(self) -> str:
        """Get appropriate device for processing."""
        if torch.cuda.is_available():
            return "cuda"
        return "cpu"
    
    def _load_model(self) -> None:
        """Load the diarization model."""
        try:
            # Try to import NeMo
            from nemo.collections.asr.models import ClusteringDiarizer
            
            self.logger.info("Loading NeMo ClusteringDiarizer")
            
            # Create diarization config
            diar_config = self._create_diarization_config()
            
            # Initialize diarizer
            self.diarizer = ClusteringDiarizer(cfg=diar_config)
            
            self.logger.info("NeMo ClusteringDiarizer loaded successfully")
            
        except ImportError:
            raise ModelLoadError("NeMo is required for speaker diarization. Please install nemo_toolkit.")
        except Exception as e:
            raise ModelLoadError(f"Failed to load diarization model: {str(e)}") from e
    
    def _create_diarization_config(self) -> OmegaConf:
        """
        Create diarization configuration for NeMo.
        
        Returns
        -------
        OmegaConf
            Diarization configuration
        """
        # Base configuration for clustering diarizer
        config = {
            "diarizer": {
                "manifest_filepath": None,
                "out_dir": None,
                "speaker_embeddings": {
                    "model_path": self.diarization_config.model_name,
                    "parameters": {
                        "window_length_in_sec": 0.5,
                        "shift_length_in_sec": 0.25,
                        "multiscale_weights": None,
                        "save_embeddings": False
                    }
                },
                "clustering": {
                    "parameters": {
                        "oracle_num_speakers": self.diarization_config.oracle_num_speakers,
                        "max_num_speakers": self.diarization_config.max_num_speakers,
                        "enhanced_count_thres": self.diarization_config.enhanced_count_thres,
                        "sparse_search_volume": self.diarization_config.sparse_search_volume,
                        "clustering_backend": self.diarization_config.clustering_backend
                    }
                },
                "vad": {
                    "model_path": "vad_multilingual_marblenet",
                    "parameters": {
                        "window_length_in_sec": 0.15,
                        "shift_length_in_sec": 0.01,
                        "smoothing": "median",
                        "overlap": 0.875,
                        "onset": 0.8,
                        "offset": 0.6,
                        "pad_onset": 0.05,
                        "pad_offset": -0.1,
                        "min_duration_on": 0.2,
                        "min_duration_off": 0.2
                    }
                }
            }
        }
        
        return OmegaConf.create(config)
    
    def process(self, input_path: Union[str, Path], output_path: Union[str, Path], **kwargs) -> Dict[str, Any]:
        """
        Process audio file for speaker diarization.
        
        Parameters
        ----------
        input_path : str or Path
            Path to input audio file
        output_path : str or Path
            Path to output directory for diarization results
        **kwargs
            Additional processing parameters:
            - oracle_num_speakers : int, optional
                Known number of speakers (if available)
            - save_embeddings : bool, default=False
                Whether to save speaker embeddings
            
        Returns
        -------
        dict
            Diarization results and metadata
        """
        input_path = self.validate_input_path(input_path)
        output_path = Path(output_path)
        self.ensure_output_dir(output_path)
        
        try:
            self.logger.info(f"Processing diarization for {input_path.name}")
            
            # Create temporary manifest file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_manifest:
                manifest_data = {
                    'audio_filepath': str(input_path),
                    'offset': 0,
                    'duration': None,
                    'label': 'infer',
                    'text': '-',
                    'num_speakers': kwargs.get('oracle_num_speakers', self.diarization_config.oracle_num_speakers),
                    'rttm_filepath': None,
                    'uem_filepath': None
                }
                
                json.dump(manifest_data, temp_manifest)
                temp_manifest_path = temp_manifest.name
            
            try:
                # Update diarizer configuration
                self.diarizer.cfg.diarizer.manifest_filepath = temp_manifest_path
                self.diarizer.cfg.diarizer.out_dir = str(output_path)
                
                # Override oracle speakers if provided
                oracle_speakers = kwargs.get('oracle_num_speakers')
                if oracle_speakers is not None:
                    self.diarizer.cfg.diarizer.clustering.parameters.oracle_num_speakers = oracle_speakers
                
                # Run diarization
                self.logger.info("Running speaker diarization...")
                self.diarizer.diarize()
                
                # Process results
                results = self._process_diarization_results(input_path, output_path)
                
                self.logger.info(f"Diarization completed: {results['num_speakers']} speakers detected")
                
                return results
                
            finally:
                # Clean up temporary manifest file
                if os.path.exists(temp_manifest_path):
                    os.unlink(temp_manifest_path)
            
        except Exception as e:
            error_msg = f"Diarization failed for {input_path.name}: {str(e)}"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg) from e
    
    def _process_diarization_results(self, input_path: Path, output_dir: Path) -> Dict[str, Any]:
        """
        Process and organize diarization results.
        
        Parameters
        ----------
        input_path : Path
            Original input audio file path
        output_dir : Path
            Output directory containing diarization results
            
        Returns
        -------
        dict
            Processed diarization results
        """
        base_name = input_path.stem
        
        # Look for RTTM file
        rttm_file = output_dir / f"{base_name}.rttm"
        if not rttm_file.exists():
            # Try alternative naming
            rttm_files = list(output_dir.glob("*.rttm"))
            if rttm_files:
                rttm_file = rttm_files[0]
            else:
                raise ProcessingError(f"No RTTM file found in {output_dir}")
        
        # Parse RTTM file
        segments = self._parse_rttm_file(rttm_file)
        
        # Convert to CSV format
        csv_file = output_dir / f"{base_name}.csv"
        self._save_segments_csv(segments, csv_file)
        
        # Calculate statistics
        stats = self._calculate_diarization_stats(segments)
        
        return {
            "input_file": str(input_path),
            "output_dir": str(output_dir),
            "rttm_file": str(rttm_file),
            "csv_file": str(csv_file),
            "segments": segments,
            "num_speakers": stats["num_speakers"],
            "num_segments": stats["num_segments"],
            "total_speech_duration": stats["total_speech_duration"],
            "speaker_durations": stats["speaker_durations"],
            "statistics": stats,
            "success": True
        }
    
    def _parse_rttm_file(self, rttm_file: Path) -> List[Dict[str, Any]]:
        """
        Parse RTTM file to extract speaker segments.
        
        Parameters
        ----------
        rttm_file : Path
            Path to RTTM file
            
        Returns
        -------
        list
            List of speaker segments
        """
        segments = []
        
        try:
            with open(rttm_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        parts = line.split()
                        if len(parts) >= 8 and parts[0] == 'SPEAKER':
                            # RTTM format: SPEAKER file_id channel_id start_time duration <NA> <NA> speaker_id <NA> <NA>
                            start_time = float(parts[3])
                            duration = float(parts[4])
                            end_time = start_time + duration
                            speaker_id = parts[7]
                            
                            segments.append({
                                'start_time': start_time,
                                'end_time': end_time,
                                'duration': duration,
                                'speaker': speaker_id
                            })
            
            # Sort segments by start time
            segments.sort(key=lambda x: x['start_time'])
            
            return segments
            
        except Exception as e:
            raise ProcessingError(f"Failed to parse RTTM file {rttm_file}: {str(e)}") from e
    
    def _save_segments_csv(self, segments: List[Dict[str, Any]], csv_file: Path) -> None:
        """
        Save segments to CSV file.
        
        Parameters
        ----------
        segments : list
            List of speaker segments
        csv_file : Path
            Output CSV file path
        """
        try:
            df = pd.DataFrame(segments)
            df.to_csv(csv_file, index=False)
            self.logger.debug(f"Segments saved to CSV: {csv_file}")
            
        except Exception as e:
            self.logger.warning(f"Failed to save segments CSV: {str(e)}")
    
    def _calculate_diarization_stats(self, segments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate statistics from diarization segments.
        
        Parameters
        ----------
        segments : list
            List of speaker segments
            
        Returns
        -------
        dict
            Diarization statistics
        """
        if not segments:
            return {
                "num_speakers": 0,
                "num_segments": 0,
                "total_speech_duration": 0,
                "speaker_durations": {},
                "avg_segment_duration": 0,
                "speaker_turn_counts": {}
            }
        
        # Calculate basic statistics
        speakers = set(seg['speaker'] for seg in segments)
        num_speakers = len(speakers)
        num_segments = len(segments)
        total_duration = sum(seg['duration'] for seg in segments)
        avg_segment_duration = total_duration / num_segments if num_segments > 0 else 0
        
        # Calculate per-speaker statistics
        speaker_durations = {}
        speaker_turn_counts = {}
        
        for speaker in speakers:
            speaker_segments = [seg for seg in segments if seg['speaker'] == speaker]
            speaker_durations[speaker] = sum(seg['duration'] for seg in speaker_segments)
            speaker_turn_counts[speaker] = len(speaker_segments)
        
        return {
            "num_speakers": num_speakers,
            "num_segments": num_segments,
            "total_speech_duration": total_duration,
            "avg_segment_duration": avg_segment_duration,
            "speaker_durations": speaker_durations,
            "speaker_turn_counts": speaker_turn_counts,
            "most_active_speaker": max(speaker_durations.items(), key=lambda x: x[1])[0] if speaker_durations else None
        }
    
    def analyze_diarization_quality(self, segments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze the quality of diarization results.
        
        Parameters
        ----------
        segments : list
            List of speaker segments
            
        Returns
        -------
        dict
            Quality analysis results
        """
        if not segments:
            return {"quality_score": 0, "issues": ["No segments found"]}
        
        issues = []
        quality_factors = []
        
        # Check for very short segments (potential over-segmentation)
        short_segments = [seg for seg in segments if seg['duration'] < 0.5]
        if len(short_segments) > len(segments) * 0.3:
            issues.append(f"Many short segments detected ({len(short_segments)}/{len(segments)})")
            quality_factors.append(0.7)
        else:
            quality_factors.append(1.0)
        
        # Check for speaker balance
        stats = self._calculate_diarization_stats(segments)
        speaker_durations = list(stats["speaker_durations"].values())
        
        if len(speaker_durations) > 1:
            duration_ratio = min(speaker_durations) / max(speaker_durations)
            if duration_ratio < 0.1:
                issues.append("Highly imbalanced speaker durations")
                quality_factors.append(0.6)
            elif duration_ratio < 0.3:
                issues.append("Moderately imbalanced speaker durations")
                quality_factors.append(0.8)
            else:
                quality_factors.append(1.0)
        
        # Check for reasonable number of speakers
        num_speakers = stats["num_speakers"]
        if num_speakers > 5:
            issues.append(f"Unusually high number of speakers detected ({num_speakers})")
            quality_factors.append(0.7)
        elif num_speakers < 1:
            issues.append("No speakers detected")
            quality_factors.append(0.0)
        else:
            quality_factors.append(1.0)
        
        # Calculate overall quality score
        quality_score = np.mean(quality_factors) if quality_factors else 0
        
        return {
            "quality_score": quality_score,
            "quality_level": "high" if quality_score > 0.8 else "medium" if quality_score > 0.6 else "low",
            "issues": issues,
            "recommendations": self._get_quality_recommendations(quality_score, issues)
        }
    
    def _get_quality_recommendations(self, quality_score: float, issues: List[str]) -> List[str]:
        """
        Get recommendations for improving diarization quality.
        
        Parameters
        ----------
        quality_score : float
            Quality score (0-1)
        issues : list
            List of detected issues
            
        Returns
        -------
        list
            List of recommendations
        """
        recommendations = []
        
        if quality_score < 0.6:
            recommendations.append("Consider using oracle number of speakers if known")
            recommendations.append("Check audio quality and preprocessing")
        
        if "short segments" in str(issues):
            recommendations.append("Consider increasing clustering threshold")
            recommendations.append("Apply post-processing to merge short segments")
        
        if "imbalanced" in str(issues):
            recommendations.append("Verify that all speakers are clearly audible")
            recommendations.append("Check for background noise or cross-talk")
        
        if "high number of speakers" in str(issues):
            recommendations.append("Set maximum number of speakers parameter")
            recommendations.append("Check for noise being classified as speakers")
        
        return recommendations
