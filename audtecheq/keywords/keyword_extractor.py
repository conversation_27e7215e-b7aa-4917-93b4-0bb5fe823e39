"""
Main keyword extraction processor for AudioTechEquity pipeline.
"""

import re
import logging
from typing import Dict, Any, List, Optional, Union
from pathlib import Path

from ..core.base import BaseProcessor
from ..core.config import KeywordConfig
from .fuzzy_matcher import FuzzyMatcher
from .gfta_analyzer import GFTAAnalyzer
from .word_repetition_analyzer import WordRepetitionAnalyzer


class KeywordExtractor(BaseProcessor):
    """
    Keyword extraction and analysis processor.
    
    Extracts and analyzes keywords from transcribed text, including
    GFTA words, word repetition tasks, and custom keyword lists.
    """
    
    def __init__(self, config: Optional[KeywordConfig] = None, **kwargs):
        """
        Initialize the keyword extractor.
        
        Parameters
        ----------
        config : KeywordConfig, optional
            Keyword configuration
        **kwargs
            Additional arguments passed to base class
        """
        super().__init__(config=config.__dict__ if config else None, **kwargs)
        
        self.keyword_config = config or KeywordConfig()
        
        # Initialize analyzers
        self.fuzzy_matcher = FuzzyMatcher(
            threshold=self.keyword_config.fuzzy_match_threshold,
            edit_distance_threshold=self.keyword_config.edit_distance_threshold,
            logger=self.logger
        )
        
        self.gfta_analyzer = GFTAAnalyzer(
            gfta_words=self.keyword_config.gfta_words,
            fuzzy_matcher=self.fuzzy_matcher,
            logger=self.logger
        )
        
        self.word_rep_analyzer = WordRepetitionAnalyzer(
            word_list=self.keyword_config.word_repetition_task,
            fuzzy_matcher=self.fuzzy_matcher,
            logger=self.logger
        )
    
    def process(self, input_path: Union[str, Path], output_path: Union[str, Path], **kwargs) -> Dict[str, Any]:
        """
        Process transcription file(s) for keyword extraction and analysis.
        
        Parameters
        ----------
        input_path : str or Path
            Path to transcription file or directory
        output_path : str or Path
            Path to output analysis file or directory
        **kwargs
            Additional processing parameters:
            - analysis_type : str, default='all'
                Type of analysis (gfta, word_repetition, custom, all)
            - custom_keywords : list, optional
                Custom keyword list for analysis
            - include_confidence : bool, default=True
                Include confidence scores in analysis
            
        Returns
        -------
        dict
            Keyword analysis results
        """
        input_path = self.validate_input_path(input_path)
        output_path = Path(output_path)
        self.ensure_output_dir(output_path.parent)
        
        try:
            self.logger.info(f"Processing keyword extraction for {input_path}")
            
            # Load transcription
            transcription = self._load_transcription(input_path)
            
            # Perform analysis based on type
            analysis_type = kwargs.get('analysis_type', 'all')
            results = self._perform_analysis(transcription, analysis_type, **kwargs)
            
            # Save results
            self._save_results(results, output_path)
            
            self.logger.info(f"Keyword analysis completed: {len(results.get('keywords', []))} keywords found")
            
            return {
                "input_file": str(input_path),
                "output_file": str(output_path),
                "analysis_type": analysis_type,
                "total_keywords": len(results.get('keywords', [])),
                "results": results,
                "success": True
            }
            
        except Exception as e:
            error_msg = f"Keyword extraction failed for {input_path}: {str(e)}"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg) from e
    
    def _load_transcription(self, input_path: Path) -> Dict[str, Any]:
        """
        Load transcription from file.
        
        Parameters
        ----------
        input_path : Path
            Path to transcription file
            
        Returns
        -------
        dict
            Transcription data
        """
        if input_path.suffix == '.json':
            import json
            with open(input_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        elif input_path.suffix == '.txt':
            with open(input_path, 'r', encoding='utf-8') as f:
                text = f.read()
            return {"text": text, "segments": []}
        
        elif input_path.suffix == '.csv':
            import pandas as pd
            df = pd.read_csv(input_path)
            
            # Combine all text
            full_text = " ".join(df['text'].astype(str))
            
            # Create segments
            segments = []
            for _, row in df.iterrows():
                segments.append({
                    "start": row.get('start_time', 0),
                    "end": row.get('end_time', 0),
                    "text": str(row['text'])
                })
            
            return {"text": full_text, "segments": segments}
        
        else:
            raise ValueError(f"Unsupported transcription format: {input_path.suffix}")
    
    def _perform_analysis(self, transcription: Dict[str, Any], analysis_type: str, **kwargs) -> Dict[str, Any]:
        """
        Perform keyword analysis on transcription.
        
        Parameters
        ----------
        transcription : dict
            Transcription data
        analysis_type : str
            Type of analysis to perform
        **kwargs
            Additional parameters
            
        Returns
        -------
        dict
            Analysis results
        """
        results = {
            "transcription_text": transcription["text"],
            "analysis_type": analysis_type,
            "keywords": [],
            "statistics": {}
        }
        
        if analysis_type in ['gfta', 'all']:
            gfta_results = self.gfta_analyzer.analyze(transcription["text"])
            results["gfta_analysis"] = gfta_results
            results["keywords"].extend(gfta_results.get("keywords", []))
        
        if analysis_type in ['word_repetition', 'all']:
            word_rep_results = self.word_rep_analyzer.analyze(transcription["text"])
            results["word_repetition_analysis"] = word_rep_results
            results["keywords"].extend(word_rep_results.get("keywords", []))
        
        if analysis_type in ['custom', 'all']:
            custom_keywords = kwargs.get('custom_keywords', [])
            if custom_keywords:
                custom_results = self.extract_keywords(transcription["text"], custom_keywords)
                results["custom_analysis"] = {
                    "keywords": custom_results,
                    "target_words": custom_keywords
                }
                results["keywords"].extend(custom_results)
        
        # Calculate overall statistics
        results["statistics"] = self._calculate_statistics(results)
        
        return results
    
    def extract_keywords(self, text: str, keyword_list: List[str], **kwargs) -> List[Dict[str, Any]]:
        """
        Extract keywords from text using fuzzy matching.
        
        Parameters
        ----------
        text : str
            Input text
        keyword_list : list
            List of keywords to search for
        **kwargs
            Additional parameters
            
        Returns
        -------
        list
            List of found keywords with metadata
        """
        found_keywords = []
        
        # Tokenize text
        words = re.findall(r'\b\w+\b', text.lower())
        
        for word in words:
            for target_keyword in keyword_list:
                match_result = self.fuzzy_matcher.match(word, target_keyword.lower())
                
                if match_result["is_match"]:
                    found_keywords.append({
                        "found_word": word,
                        "target_keyword": target_keyword,
                        "similarity_score": match_result["similarity"],
                        "edit_distance": match_result["edit_distance"],
                        "exact_match": match_result["exact_match"],
                        "match_type": match_result["match_type"]
                    })
        
        return found_keywords
    
    def _calculate_statistics(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate overall statistics for keyword analysis.
        
        Parameters
        ----------
        results : dict
            Analysis results
            
        Returns
        -------
        dict
            Statistics
        """
        keywords = results.get("keywords", [])
        
        if not keywords:
            return {
                "total_keywords": 0,
                "exact_matches": 0,
                "fuzzy_matches": 0,
                "average_similarity": 0.0
            }
        
        exact_matches = sum(1 for kw in keywords if kw.get("exact_match", False))
        fuzzy_matches = len(keywords) - exact_matches
        
        similarities = [kw.get("similarity_score", 0) for kw in keywords]
        avg_similarity = sum(similarities) / len(similarities) if similarities else 0
        
        stats = {
            "total_keywords": len(keywords),
            "exact_matches": exact_matches,
            "fuzzy_matches": fuzzy_matches,
            "exact_match_rate": exact_matches / len(keywords) if keywords else 0,
            "average_similarity": avg_similarity
        }
        
        # Add analysis-specific statistics
        if "gfta_analysis" in results:
            gfta_stats = results["gfta_analysis"].get("statistics", {})
            stats["gfta_statistics"] = gfta_stats
        
        if "word_repetition_analysis" in results:
            word_rep_stats = results["word_repetition_analysis"].get("statistics", {})
            stats["word_repetition_statistics"] = word_rep_stats
        
        return stats
    
    def _save_results(self, results: Dict[str, Any], output_path: Path) -> None:
        """
        Save analysis results to file.
        
        Parameters
        ----------
        results : dict
            Analysis results
        output_path : Path
            Output file path
        """
        import json
        
        # Ensure JSON extension
        if output_path.suffix != '.json':
            output_path = output_path.with_suffix('.json')
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
    
    def analyze_transcription_quality(self, transcription: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze the quality of transcription for keyword extraction.
        
        Parameters
        ----------
        transcription : dict
            Transcription data
            
        Returns
        -------
        dict
            Quality analysis
        """
        text = transcription["text"]
        
        # Basic text quality metrics
        word_count = len(text.split())
        char_count = len(text)
        
        # Check for common transcription issues
        issues = []
        
        # Very short transcription
        if word_count < 5:
            issues.append("Very short transcription (< 5 words)")
        
        # High ratio of single characters (potential transcription errors)
        single_chars = len([w for w in text.split() if len(w) == 1])
        if single_chars / word_count > 0.3 if word_count > 0 else False:
            issues.append("High ratio of single characters")
        
        # Repeated words (potential transcription artifacts)
        words = text.lower().split()
        unique_words = set(words)
        repetition_ratio = len(words) / len(unique_words) if unique_words else 0
        if repetition_ratio > 2.0:
            issues.append("High word repetition ratio")
        
        # Calculate quality score
        quality_score = 1.0
        if issues:
            quality_score -= len(issues) * 0.2
        quality_score = max(0.0, quality_score)
        
        return {
            "word_count": word_count,
            "character_count": char_count,
            "unique_words": len(unique_words),
            "repetition_ratio": repetition_ratio,
            "quality_score": quality_score,
            "issues": issues,
            "recommendations": self._get_quality_recommendations(issues)
        }
    
    def _get_quality_recommendations(self, issues: List[str]) -> List[str]:
        """
        Get recommendations based on quality issues.
        
        Parameters
        ----------
        issues : list
            List of quality issues
            
        Returns
        -------
        list
            List of recommendations
        """
        recommendations = []
        
        if "Very short transcription" in str(issues):
            recommendations.append("Check audio quality and duration")
            recommendations.append("Verify ASR model settings")
        
        if "High ratio of single characters" in str(issues):
            recommendations.append("Review ASR confidence thresholds")
            recommendations.append("Consider audio preprocessing")
        
        if "High word repetition" in str(issues):
            recommendations.append("Check for audio artifacts or loops")
            recommendations.append("Verify speaker diarization quality")
        
        return recommendations
