"""
Fuzzy matching for keyword recognition.
"""

import logging
from typing import Dict, Any, Optional
from difflib import SequenceMatch<PERSON>

try:
    from fuzzywuzzy import fuzz
    FUZZYWUZZY_AVAILABLE = True
except ImportError:
    FUZZYWUZZY_AVAILABLE = False


class FuzzyMatcher:
    """
    Fuzzy string matching for keyword recognition.
    """
    
    def __init__(self, threshold: float = 0.8, edit_distance_threshold: int = 2, 
                 logger: Optional[logging.Logger] = None):
        """
        Initialize fuzzy matcher.
        
        Parameters
        ----------
        threshold : float
            Similarity threshold for fuzzy matching (0-1)
        edit_distance_threshold : int
            Maximum edit distance for matches
        logger : logging.Logger, optional
            Logger instance
        """
        self.threshold = threshold
        self.edit_distance_threshold = edit_distance_threshold
        self.logger = logger or logging.getLogger(__name__)
        
        if not FUZZYWUZZY_AVAILABLE:
            self.logger.warning("fuzzywuzzy not available, using basic string matching")
    
    def match(self, word: str, target: str) -> Dict[str, Any]:
        """
        Check if word matches target using fuzzy matching.
        
        Parameters
        ----------
        word : str
            Word to match
        target : str
            Target word
            
        Returns
        -------
        dict
            Match result with similarity score and metadata
        """
        word = word.lower().strip()
        target = target.lower().strip()
        
        # Exact match
        if word == target:
            return {
                "is_match": True,
                "exact_match": True,
                "similarity": 1.0,
                "edit_distance": 0,
                "match_type": "exact"
            }
        
        # Calculate similarity
        if FUZZYWUZZY_AVAILABLE:
            similarity = fuzz.ratio(word, target) / 100.0
        else:
            similarity = SequenceMatcher(None, word, target).ratio()
        
        # Calculate edit distance
        edit_distance = self._calculate_edit_distance(word, target)
        
        # Determine if it's a match
        is_match = (similarity >= self.threshold or 
                   edit_distance <= self.edit_distance_threshold)
        
        # Determine match type
        match_type = "none"
        if is_match:
            if similarity >= 0.95:
                match_type = "near_exact"
            elif similarity >= self.threshold:
                match_type = "fuzzy"
            elif edit_distance <= self.edit_distance_threshold:
                match_type = "edit_distance"
        
        return {
            "is_match": is_match,
            "exact_match": False,
            "similarity": similarity,
            "edit_distance": edit_distance,
            "match_type": match_type
        }
    
    def _calculate_edit_distance(self, word1: str, word2: str) -> int:
        """
        Calculate Levenshtein edit distance between two words.
        
        Parameters
        ----------
        word1 : str
            First word
        word2 : str
            Second word
            
        Returns
        -------
        int
            Edit distance
        """
        if len(word1) < len(word2):
            return self._calculate_edit_distance(word2, word1)
        
        if len(word2) == 0:
            return len(word1)
        
        previous_row = list(range(len(word2) + 1))
        for i, c1 in enumerate(word1):
            current_row = [i + 1]
            for j, c2 in enumerate(word2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        
        return previous_row[-1]
