"""
Word repetition task analyzer for AudioTechEquity pipeline.
"""

import logging
from typing import Dict, Any, List, Optional

from .fuzzy_matcher import FuzzyMatcher


class WordRepetitionAnalyzer:
    """
    Analyzer for word repetition tasks.
    """
    
    def __init__(self, word_list: List[str], fuzzy_matcher: <PERSON><PERSON><PERSON><PERSON><PERSON>,
                 logger: Optional[logging.Logger] = None):
        """
        Initialize word repetition analyzer.
        
        Parameters
        ----------
        word_list : list
            List of target words for repetition task
        fuzzy_matcher : FuzzyMatcher
            Fuzzy matching instance
        logger : logging.Logger, optional
            Logger instance
        """
        self.word_list = [word.lower() for word in word_list]
        self.fuzzy_matcher = fuzzy_matcher
        self.logger = logger or logging.getLogger(__name__)
    
    def analyze(self, text: str) -> Dict[str, Any]:
        """
        Analyze text for word repetition task performance.
        
        Parameters
        ----------
        text : str
            Transcribed text to analyze
            
        Returns
        -------
        dict
            Word repetition analysis results
        """
        import re
        
        # Tokenize text
        words = re.findall(r'\b\w+\b', text.lower())
        
        # Find repetition words
        found_keywords = []
        repetition_statistics = {}
        
        for word in words:
            for target_word in self.word_list:
                match_result = self.fuzzy_matcher.match(word, target_word)
                
                if match_result["is_match"]:
                    found_keywords.append({
                        "found_word": word,
                        "target_keyword": target_word,
                        "similarity_score": match_result["similarity"],
                        "edit_distance": match_result["edit_distance"],
                        "exact_match": match_result["exact_match"],
                        "match_type": match_result["match_type"]
                    })
                    
                    # Update statistics
                    if target_word not in repetition_statistics:
                        repetition_statistics[target_word] = {
                            "repetitions": 0,
                            "correct_repetitions": 0,
                            "productions": []
                        }
                    
                    repetition_statistics[target_word]["repetitions"] += 1
                    if match_result["exact_match"]:
                        repetition_statistics[target_word]["correct_repetitions"] += 1
                    
                    repetition_statistics[target_word]["productions"].append({
                        "production": word,
                        "correct": match_result["exact_match"],
                        "similarity": match_result["similarity"]
                    })
        
        # Calculate overall statistics
        total_target_words = len(self.word_list)
        words_attempted = len(repetition_statistics)
        words_correct = sum(1 for stats in repetition_statistics.values() 
                           if stats["correct_repetitions"] > 0)
        
        total_repetitions = sum(stats["repetitions"] for stats in repetition_statistics.values())
        correct_repetitions = sum(stats["correct_repetitions"] for stats in repetition_statistics.values())
        
        repetition_accuracy = correct_repetitions / total_repetitions if total_repetitions > 0 else 0
        word_coverage = words_attempted / total_target_words if total_target_words > 0 else 0
        
        return {
            "keywords": found_keywords,
            "repetition_statistics": repetition_statistics,
            "statistics": {
                "total_target_words": total_target_words,
                "words_attempted": words_attempted,
                "words_with_correct_repetitions": words_correct,
                "total_repetitions": total_repetitions,
                "correct_repetitions": correct_repetitions,
                "repetition_accuracy": repetition_accuracy,
                "word_coverage": word_coverage
            },
            "repetition_patterns": self._analyze_repetition_patterns(repetition_statistics)
        }
    
    def _analyze_repetition_patterns(self, repetition_statistics: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze repetition patterns and errors.
        
        Parameters
        ----------
        repetition_statistics : dict
            Word-level repetition statistics
            
        Returns
        -------
        dict
            Repetition pattern analysis
        """
        patterns = {
            "consistent_errors": [],
            "inconsistent_performance": [],
            "perfect_repetitions": [],
            "error_types": {
                "phonological": [],
                "morphological": [],
                "lexical": []
            }
        }
        
        for target_word, stats in repetition_statistics.items():
            total_reps = stats["repetitions"]
            correct_reps = stats["correct_repetitions"]
            
            if correct_reps == total_reps and total_reps > 0:
                patterns["perfect_repetitions"].append(target_word)
            elif correct_reps == 0 and total_reps > 0:
                patterns["consistent_errors"].append({
                    "word": target_word,
                    "attempts": total_reps,
                    "productions": [p["production"] for p in stats["productions"]]
                })
            elif total_reps > 1:
                patterns["inconsistent_performance"].append({
                    "word": target_word,
                    "attempts": total_reps,
                    "correct": correct_reps,
                    "accuracy": correct_reps / total_reps
                })
        
        return patterns
