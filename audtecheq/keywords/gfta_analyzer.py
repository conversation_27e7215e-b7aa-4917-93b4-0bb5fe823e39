"""
GFTA (Goldman-Fristoe Test of Articulation) word analysis.
"""

import logging
from typing import Dict, Any, List, Optional

from .fuzzy_matcher import FuzzyMatcher


class GFTAAnalyzer:
    """
    Analyzer for GFTA word recognition and articulation assessment.
    """
    
    def __init__(self, gfta_words: List[str], fuzzy_matcher: <PERSON><PERSON><PERSON>atcher,
                 logger: Optional[logging.Logger] = None):
        """
        Initialize GFTA analyzer.
        
        Parameters
        ----------
        gfta_words : list
            List of GFTA target words
        fuzzy_matcher : FuzzyMatcher
            Fuzzy matching instance
        logger : logging.Logger, optional
            Logger instance
        """
        self.gfta_words = [word.lower() for word in gfta_words]
        self.fuzzy_matcher = fuzzy_matcher
        self.logger = logger or logging.getLogger(__name__)
    
    def analyze(self, text: str) -> Dict[str, Any]:
        """
        Analyze text for GFTA word recognition.
        
        Parameters
        ----------
        text : str
            Transcribed text to analyze
            
        Returns
        -------
        dict
            GFTA analysis results
        """
        import re
        
        # Tokenize text
        words = re.findall(r'\b\w+\b', text.lower())
        
        # Find GFTA words
        found_keywords = []
        word_statistics = {}
        
        for word in words:
            for gfta_word in self.gfta_words:
                match_result = self.fuzzy_matcher.match(word, gfta_word)
                
                if match_result["is_match"]:
                    found_keywords.append({
                        "found_word": word,
                        "target_keyword": gfta_word,
                        "similarity_score": match_result["similarity"],
                        "edit_distance": match_result["edit_distance"],
                        "exact_match": match_result["exact_match"],
                        "match_type": match_result["match_type"]
                    })
                    
                    # Update statistics
                    if gfta_word not in word_statistics:
                        word_statistics[gfta_word] = {
                            "attempts": 0,
                            "correct": 0,
                            "productions": []
                        }
                    
                    word_statistics[gfta_word]["attempts"] += 1
                    if match_result["exact_match"]:
                        word_statistics[gfta_word]["correct"] += 1
                    
                    word_statistics[gfta_word]["productions"].append({
                        "production": word,
                        "correct": match_result["exact_match"],
                        "similarity": match_result["similarity"]
                    })
        
        # Calculate overall statistics
        total_gfta_words = len(self.gfta_words)
        words_attempted = len(word_statistics)
        words_correct = sum(1 for stats in word_statistics.values() if stats["correct"] > 0)
        
        accuracy = words_correct / words_attempted if words_attempted > 0 else 0
        coverage = words_attempted / total_gfta_words if total_gfta_words > 0 else 0
        
        return {
            "keywords": found_keywords,
            "word_statistics": word_statistics,
            "statistics": {
                "total_gfta_words": total_gfta_words,
                "words_attempted": words_attempted,
                "words_correct": words_correct,
                "accuracy": accuracy,
                "coverage": coverage,
                "total_productions": len(found_keywords)
            },
            "articulation_patterns": self._analyze_articulation_patterns(word_statistics)
        }
    
    def _analyze_articulation_patterns(self, word_statistics: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze articulation patterns from GFTA results.
        
        Parameters
        ----------
        word_statistics : dict
            Word-level statistics
            
        Returns
        -------
        dict
            Articulation pattern analysis
        """
        patterns = {
            "substitutions": [],
            "omissions": [],
            "distortions": [],
            "common_errors": {}
        }
        
        for target_word, stats in word_statistics.items():
            for production in stats["productions"]:
                if not production["correct"]:
                    # Analyze error type
                    target = target_word
                    produced = production["production"]
                    
                    if len(produced) < len(target):
                        patterns["omissions"].append({
                            "target": target,
                            "production": produced,
                            "similarity": production["similarity"]
                        })
                    elif len(produced) == len(target):
                        patterns["substitutions"].append({
                            "target": target,
                            "production": produced,
                            "similarity": production["similarity"]
                        })
                    else:
                        patterns["distortions"].append({
                            "target": target,
                            "production": produced,
                            "similarity": production["similarity"]
                        })
                    
                    # Track common error patterns
                    error_pattern = f"{target} → {produced}"
                    if error_pattern not in patterns["common_errors"]:
                        patterns["common_errors"][error_pattern] = 0
                    patterns["common_errors"][error_pattern] += 1
        
        return patterns
