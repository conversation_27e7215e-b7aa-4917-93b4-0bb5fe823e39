
from ._version import get_versions
__version__ = get_versions()['version']
del get_versions

try:
    from ._version import __version__
except ImportError:
    __version__ = "0+unknown"

# Core components
from .core import PipelineConfig, BaseProcessor
from .preprocessing import AudioPreprocessor
from .vad import VADProcessor, SilenceProcessor
from .diarization import SpeakerDiarizer
from .segmentation import AudioSegmenter
from .asr import ASRProcessor
from .keywords import KeywordExtractor
from .testing import ASRTester, DiarizationTester, EnhancedDiarizationTester

# CLI entry point
from .cli import main_cli

# Boolean controlling the default globbing technique when using check_niimg
# and the os.path.expanduser usage in CacheMixin.
# Default value it True, set it to False to completely deactivate this
# behavior.
EXPAND_PATH_WILDCARDS = True

# list all submodules available in audtecheq and version
__all__ = [
    "__version__",
    "PipelineConfig",
    "BaseProcessor",
    "AudioPreprocessor",
    "VADProcessor",
    "SpeakerDiarizer",
    "AudioSegmenter",
    "ASRProcessor",
    "KeywordExtractor",
    "ASRTester",
    "DiarizationTester",
    "EnhancedDiarizationTester",
    "SilenceProcessor",
    "main_cli",
    "utils",
    "conversion",
    "data",
    "core",
    "preprocessing",
    "vad",
    "diarization",
    "segmentation",
    "asr",
    "keywords",
    "testing",
    "cli"
]
