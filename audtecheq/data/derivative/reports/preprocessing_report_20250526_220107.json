{"preprocessing_report": {"timestamp": "2025-05-26T22:01:08.315133", "summary": {"total_files": 106, "successful_files": 0, "failed_files": 106, "success_rate": 0.0, "total_processing_time": 0.5335080000000001, "average_processing_time": 0.005033094339622643}, "participants": {"sub-ATL001": {"total": 5, "successful": 0, "failed": 5}, "sub-ATL002": {"total": 18, "successful": 0, "failed": 18}, "sub-ATL003": {"total": 31, "successful": 0, "failed": 31}, "sub-ATL004": {"total": 20, "successful": 0, "failed": 20}, "sub-ATL005": {"total": 32, "successful": 0, "failed": 32}}, "configuration": {"sample_rate": 16000, "channels": 1, "target_dbfs": -20.0, "noise_reduction": 0.8}, "detailed_results": [{"input_file": "audtecheq/data/derivative/preprocessed/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/derivative/preprocessed/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicX.wav", "success": false, "processing_time": 0.009073, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/derivative/preprocessed/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/derivative/preprocessed/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.754944"}, {"input_file": "audtecheq/data/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicX.wav", "success": false, "processing_time": 0.005553, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.764199"}, {"input_file": "audtecheq/data/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicXY.wav", "success": false, "processing_time": 0.004856, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.769975"}, {"input_file": "audtecheq/data/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicY.wav", "success": false, "processing_time": 0.005933, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.775054"}, {"input_file": "audtecheq/data/sub-ATL001/beh/sub-ATL001_task-FTL_acq-ZA.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL001/beh/sub-ATL001_task-FTL_acq-ZA.wav", "success": false, "processing_time": 0.004442, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL001/beh/sub-ATL001_task-FTL_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL001/beh/sub-ATL001_task-FTL_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.781165"}, {"input_file": "audtecheq/data/sub-ATL002/beh/sub-ATL002_task-celfp3.screen_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-celfp3.screen_acq-MicX.wav", "success": false, "processing_time": 0.003958, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-celfp3.screen_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-celfp3.screen_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.785902"}, {"input_file": "audtecheq/data/sub-ATL002/beh/sub-ATL002_task-celfp3.screen_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-celfp3.screen_acq-MicXY.wav", "success": false, "processing_time": 0.004449, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-celfp3.screen_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-celfp3.screen_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.790034"}, {"input_file": "audtecheq/data/sub-ATL002/beh/sub-ATL002_task-celfp3.screen_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-celfp3.screen_acq-MicY.wav", "success": false, "processing_time": 0.009942, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-celfp3.screen_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-celfp3.screen_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.794681"}, {"input_file": "audtecheq/data/sub-ATL002/beh/sub-ATL002_task-gfta.test_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-gfta.test_acq-MicX.wav", "success": false, "processing_time": 0.007448, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-gfta.test_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-gfta.test_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.805317"}, {"input_file": "audtecheq/data/sub-ATL002/beh/sub-ATL002_task-gfta.test_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-gfta.test_acq-MicXY.wav", "success": false, "processing_time": 0.008048, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-gfta.test_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-gfta.test_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.813002"}, {"input_file": "audtecheq/data/sub-ATL002/beh/sub-ATL002_task-gfta.test_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-gfta.test_acq-MicY.wav", "success": false, "processing_time": 0.006973, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-gfta.test_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-gfta.test_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.821466"}, {"input_file": "audtecheq/data/sub-ATL002/beh/sub-ATL002_task-picturetask_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-picturetask_acq-MicX.wav", "success": false, "processing_time": 0.009913, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-picturetask_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-picturetask_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.829409"}, {"input_file": "audtecheq/data/sub-ATL002/beh/sub-ATL002_task-picturetask_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-picturetask_acq-MicXY.wav", "success": false, "processing_time": 0.005315, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-picturetask_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-picturetask_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.839673"}, {"input_file": "audtecheq/data/sub-ATL002/beh/sub-ATL002_task-picturetask_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-picturetask_acq-MicY.wav", "success": false, "processing_time": 0.004965, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-picturetask_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-picturetask_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.845259"}, {"input_file": "audtecheq/data/sub-ATL002/beh/sub-ATL002_task-pls5.screen_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-pls5.screen_acq-MicX.wav", "success": false, "processing_time": 0.00522, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-pls5.screen_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-pls5.screen_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.850487"}, {"input_file": "audtecheq/data/sub-ATL002/beh/sub-ATL002_task-pls5.screen_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-pls5.screen_acq-MicXY.wav", "success": false, "processing_time": 0.005396, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-pls5.screen_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-pls5.screen_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.855990"}, {"input_file": "audtecheq/data/sub-ATL002/beh/sub-ATL002_task-pls5.screen_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-pls5.screen_acq-MicY.wav", "success": false, "processing_time": 0.005167, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-pls5.screen_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-pls5.screen_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.861611"}, {"input_file": "audtecheq/data/sub-ATL002/beh/sub-ATL002_task-toyplay_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-toyplay_acq-MicX.wav", "success": false, "processing_time": 0.005494, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-toyplay_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-toyplay_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.867022"}, {"input_file": "audtecheq/data/sub-ATL002/beh/sub-ATL002_task-toyplay_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-toyplay_acq-MicXY.wav", "success": false, "processing_time": 0.005346, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-toyplay_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-toyplay_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.872860"}, {"input_file": "audtecheq/data/sub-ATL002/beh/sub-ATL002_task-toyplay_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-toyplay_acq-MicY.wav", "success": false, "processing_time": 0.004859, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-toyplay_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-toyplay_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.878361"}, {"input_file": "audtecheq/data/sub-ATL002/beh/sub-ATL002_task-warmup_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-warmup_acq-MicX.wav", "success": false, "processing_time": 0.005159, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-warmup_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-warmup_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.883460"}, {"input_file": "audtecheq/data/sub-ATL002/beh/sub-ATL002_task-warmup_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-warmup_acq-MicXY.wav", "success": false, "processing_time": 0.003856, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-warmup_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-warmup_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.888832"}, {"input_file": "audtecheq/data/sub-ATL002/beh/sub-ATL002_task-warmup_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-warmup_acq-MicY.wav", "success": false, "processing_time": 0.010912, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-warmup_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-warmup_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.892939"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-MicX.wav", "success": false, "processing_time": 0.004851, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.904328"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-MicXY.wav", "success": false, "processing_time": 0.004093, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.909454"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-MicY.wav", "success": false, "processing_time": 0.00354, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.913761"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-ZA.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-ZA.wav", "success": false, "processing_time": 0.003616, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.917440"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-celfp3.test_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-celfp3.test_acq-MicX.wav", "success": false, "processing_time": 0.003082, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-celfp3.test_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-celfp3.test_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.921184"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-celfp3.test_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-celfp3.test_acq-MicY.wav", "success": false, "processing_time": 0.003587, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-celfp3.test_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-celfp3.test_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.924435"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-celfp3.test_acq-ZA.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-celfp3.test_acq-ZA.wav", "success": false, "processing_time": 0.00423, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-celfp3.test_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-celfp3.test_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.928158"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-MicX.wav", "success": false, "processing_time": 0.004165, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.932585"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-MicXY.wav", "success": false, "processing_time": 0.004839, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.936980"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-MicY.wav", "success": false, "processing_time": 0.004513, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.942068"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-ZA.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-ZA.wav", "success": false, "processing_time": 0.003925, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.946777"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-MicX.wav", "success": false, "processing_time": 0.004162, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.950927"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-MicXY.wav", "success": false, "processing_time": 0.007337, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.955274"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-MicY.wav", "success": false, "processing_time": 0.005169, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.962820"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-ZA.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-ZA.wav", "success": false, "processing_time": 0.005478, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.968285"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-MicX.wav", "success": false, "processing_time": 0.004377, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.974005"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-MicXY.wav", "success": false, "processing_time": 0.004528, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.978558"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-MicY.wav", "success": false, "processing_time": 0.003394, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.983250"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-ZA.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-ZA.wav", "success": false, "processing_time": 0.003973, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.986833"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-MicX.wav", "success": false, "processing_time": 0.005244, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.990995"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-MicXY.wav", "success": false, "processing_time": 0.005253, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:07.996504"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-MicY.wav", "success": false, "processing_time": 0.005083, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.002031"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-ZA.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-ZA.wav", "success": false, "processing_time": 0.004621, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.007442"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-MicX.wav", "success": false, "processing_time": 0.003563, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.012333"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-MicXY.wav", "success": false, "processing_time": 0.003537, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.016157"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-MicY.wav", "success": false, "processing_time": 0.006174, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.019899"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-ZA.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-ZA.wav", "success": false, "processing_time": 0.005054, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.026301"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-warmup_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-warmup_acq-MicX.wav", "success": false, "processing_time": 0.003604, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-warmup_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-warmup_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.031504"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-warmup_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-warmup_acq-MicXY.wav", "success": false, "processing_time": 0.00507, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-warmup_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-warmup_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.035380"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-warmup_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-warmup_acq-MicY.wav", "success": false, "processing_time": 0.004007, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-warmup_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-warmup_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.040752"}, {"input_file": "audtecheq/data/sub-ATL003/beh/sub-ATL003_task-warmup_acq-ZA.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-warmup_acq-ZA.wav", "success": false, "processing_time": 0.002986, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-warmup_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-warmup_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.044965"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-MicX.wav", "success": false, "processing_time": 0.003091, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.048287"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-MicXY.wav", "success": false, "processing_time": 0.004653, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.051645"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-MicY.wav", "success": false, "processing_time": 0.005467, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.056558"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-ZA.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-ZA.wav", "success": false, "processing_time": 0.00404, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.062332"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-MicX.wav", "success": false, "processing_time": 0.003938, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.066598"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-MicXY.wav", "success": false, "processing_time": 0.00601, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.070712"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-MicY.wav", "success": false, "processing_time": 0.005267, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.076927"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-ZA.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-ZA.wav", "success": false, "processing_time": 0.004903, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.082406"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-MicX.wav", "success": false, "processing_time": 0.003168, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.087541"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-MicXY.wav", "success": false, "processing_time": 0.003757, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.090977"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-MicY.wav", "success": false, "processing_time": 0.006043, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.094933"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-ZA.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-ZA.wav", "success": false, "processing_time": 0.005079, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.101482"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-MicX.wav", "success": false, "processing_time": 0.005203, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.106901"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-MicXY.wav", "success": false, "processing_time": 0.004408, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.112306"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-MicY.wav", "success": false, "processing_time": 0.004095, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.116992"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-ZA.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-ZA.wav", "success": false, "processing_time": 0.005778, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.121272"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-warmup_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-warmup_acq-MicX.wav", "success": false, "processing_time": 0.005081, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-warmup_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-warmup_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.127296"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-warmup_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-warmup_acq-MicXY.wav", "success": false, "processing_time": 0.005226, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-warmup_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-warmup_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.132621"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-warmup_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-warmup_acq-MicY.wav", "success": false, "processing_time": 0.004969, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-warmup_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-warmup_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.138083"}, {"input_file": "audtecheq/data/sub-ATL004/beh/sub-ATL004_task-warmup_acq-ZA.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-warmup_acq-ZA.wav", "success": false, "processing_time": 0.006325, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-warmup_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-warmup_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.143256"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-MicX.wav", "success": false, "processing_time": 0.004845, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.149875"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-MicXY.wav", "success": false, "processing_time": 0.00309, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.154935"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-MicY.wav", "success": false, "processing_time": 0.00641, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.158197"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-ZA.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-ZA.wav", "success": false, "processing_time": 0.006005, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.164777"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test.2_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test.2_acq-MicX.wav", "success": false, "processing_time": 0.005451, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test.2_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test.2_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.170962"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test.2_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test.2_acq-MicXY.wav", "success": false, "processing_time": 0.012393, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test.2_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test.2_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.176742"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test.2_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test.2_acq-MicY.wav", "success": false, "processing_time": 0.004677, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test.2_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test.2_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.189456"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test.NA_acq-ZA.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test.NA_acq-ZA.wav", "success": false, "processing_time": 0.006679, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test.NA_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test.NA_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.194389"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-MicX.wav", "success": false, "processing_time": 0.004934, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.201250"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-MicXY.wav", "success": false, "processing_time": 0.006077, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.206414"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-MicY.wav", "success": false, "processing_time": 0.005027, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.212770"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-ZA.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-ZA.wav", "success": false, "processing_time": 0.004934, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.217960"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-MicX.wav", "success": false, "processing_time": 0.006095, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.223112"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-MicXY.wav", "success": false, "processing_time": 0.004918, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.229453"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-MicY.wav", "success": false, "processing_time": 0.005058, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.234627"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-ZA.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-ZA.wav", "success": false, "processing_time": 0.003307, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.239963"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-MicX.wav", "success": false, "processing_time": 0.006558, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.243498"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-MicXY.wav", "success": false, "processing_time": 0.004753, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.250296"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-MicY.wav", "success": false, "processing_time": 0.003232, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.255346"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-ZA.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-ZA.wav", "success": false, "processing_time": 0.003207, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.258801"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-MicX.wav", "success": false, "processing_time": 0.007107, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.262206"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-MicXY.wav", "success": false, "processing_time": 0.005024, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.269611"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-MicY.wav", "success": false, "processing_time": 0.003914, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.274857"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-ZA.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-ZA.wav", "success": false, "processing_time": 0.003089, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.279028"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-MicX.wav", "success": false, "processing_time": 0.003197, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.282329"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-MicXY.wav", "success": false, "processing_time": 0.002981, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.285693"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-MicY.wav", "success": false, "processing_time": 0.005619, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.288857"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-ZA.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-ZA.wav", "success": false, "processing_time": 0.004988, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.294758"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-warmup_acq-MicX.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-warmup_acq-MicX.wav", "success": false, "processing_time": 0.005118, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-warmup_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-warmup_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.299959"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-warmup_acq-MicXY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-warmup_acq-MicXY.wav", "success": false, "processing_time": 0.002955, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-warmup_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-warmup_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.305318"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-warmup_acq-MicY.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-warmup_acq-MicY.wav", "success": false, "processing_time": 0.003116, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-warmup_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-warmup_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.308393"}, {"input_file": "audtecheq/data/sub-ATL005/beh/sub-ATL005_task-warmup_acq-ZA.wav", "output_file": "audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-warmup_acq-ZA.wav", "success": false, "processing_time": 0.002917, "command": "python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-warmup_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-warmup_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO", "stdout": "", "stderr": "[Errno 2] No such file or directory: 'python'", "timestamp": "2025-05-26T22:01:08.311757"}]}}