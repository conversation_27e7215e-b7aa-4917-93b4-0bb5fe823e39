"""
Audio denoising functionality.
"""

import logging
import numpy as np
from typing import Dict, Any, Union, Optional
from pathlib import Path

import soundfile as sf
import noisereduce as nr

from ..core.base import BaseProcessor
from ..core.exceptions import ProcessingError


class AudioDenoiser(BaseProcessor):
    """
    Audio denoising processor.

    Handles noise reduction using spectral gating and other techniques.
    """

    def __init__(self, prop_decrease: float = 0.8, stationary: bool = True,
                 logger: Optional[logging.Logger] = None):
        """
        Initialize the audio denoiser.

        Parameters
        ----------
        prop_decrease : float, default=0.8
            Proportion of noise to reduce (0.0 to 1.0)
            1.0 = maximum reduction, 0.8 = mild reduction
        stationary : bool, default=True
            Whether to assume background noise is stationary
        logger : logging.Logger, optional
            Logger instance
        """
        # Set attributes before calling super().__init__ so _validate_config can access them
        self.prop_decrease = prop_decrease
        self.stationary = stationary

        config = {
            'prop_decrease': prop_decrease,
            'stationary': stationary
        }
        super().__init__(config=config, logger=logger)

    def _validate_config(self) -> None:
        """Validate denoiser configuration."""
        if not 0.0 <= self.prop_decrease <= 1.0:
            raise ValueError("prop_decrease must be between 0.0 and 1.0")

    def process(self, input_path: Union[str, Path], output_path: Union[str, Path], **kwargs) -> Dict[str, Any]:
        """
        Apply noise reduction to audio file.

        Parameters
        ----------
        input_path : str or Path
            Path to input audio file
        output_path : str or Path
            Path to output denoised audio file
        **kwargs
            Additional processing parameters:
            - noise_clip : array-like, optional
                Specific noise sample to use for noise profiling
            - use_torch : bool, default=False
                Whether to use PyTorch backend for processing

        Returns
        -------
        dict
            Denoising results and metadata
        """
        input_path = self.validate_input_path(input_path)
        output_path = Path(output_path)
        self.ensure_output_dir(output_path.parent)

        try:
            # Load audio file
            self.logger.debug(f"Loading audio file: {input_path}")
            audio, sample_rate = sf.read(str(input_path))

            # Get original audio properties
            original_shape = audio.shape
            original_duration = len(audio) / sample_rate

            self.logger.info(f"Original audio: {original_shape}, {sample_rate}Hz, {original_duration:.2f}s")

            # Convert to mono if stereo
            if len(audio.shape) > 1:
                self.logger.debug("Converting stereo to mono")
                audio = np.mean(audio, axis=1)

            # Calculate noise statistics before processing
            noise_floor_before = self._estimate_noise_floor(audio)

            # Apply noise reduction
            self.logger.debug(f"Applying noise reduction (prop_decrease={self.prop_decrease}, stationary={self.stationary})")

            # Get additional parameters
            noise_clip = kwargs.get('noise_clip', None)
            use_torch = kwargs.get('use_torch', False)

            # Apply noise reduction
            if use_torch:
                try:
                    reduced_noise = nr.reduce_noise(
                        y=audio,
                        sr=sample_rate,
                        prop_decrease=self.prop_decrease,
                        stationary=self.stationary,
                        use_torch=True,
                        device='cuda' if self._cuda_available() else 'cpu'
                    )
                except Exception as e:
                    self.logger.warning(f"PyTorch backend failed, falling back to numpy: {e}")
                    reduced_noise = nr.reduce_noise(
                        y=audio,
                        sr=sample_rate,
                        prop_decrease=self.prop_decrease,
                        stationary=self.stationary
                    )
            else:
                reduced_noise = nr.reduce_noise(
                    y=audio,
                    sr=sample_rate,
                    prop_decrease=self.prop_decrease,
                    stationary=self.stationary
                )

            # Calculate noise statistics after processing
            noise_floor_after = self._estimate_noise_floor(reduced_noise)
            noise_reduction_db = 20 * np.log10(noise_floor_before / max(noise_floor_after, 1e-10))

            # Save denoised audio
            self.logger.debug(f"Saving denoised audio to: {output_path}")
            sf.write(str(output_path), reduced_noise, sample_rate)

            self.logger.info(f"Noise reduction applied: {noise_reduction_db:.2f} dB reduction")

            return {
                "input_file": str(input_path),
                "output_file": str(output_path),
                "original_shape": original_shape,
                "processed_shape": reduced_noise.shape,
                "sample_rate": sample_rate,
                "duration": original_duration,
                "prop_decrease": self.prop_decrease,
                "stationary": self.stationary,
                "noise_floor_before": noise_floor_before,
                "noise_floor_after": noise_floor_after,
                "noise_reduction_db": noise_reduction_db,
                "used_torch": use_torch and self._cuda_available(),
                "success": True
            }

        except Exception as e:
            error_msg = f"Denoising failed for {input_path.name}: {str(e)}"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg) from e

    def _estimate_noise_floor(self, audio: np.ndarray, percentile: float = 10) -> float:
        """
        Estimate the noise floor of an audio signal.

        Parameters
        ----------
        audio : np.ndarray
            Audio signal
        percentile : float, default=10
            Percentile to use for noise floor estimation

        Returns
        -------
        float
            Estimated noise floor (RMS value)
        """
        # Calculate RMS in small windows
        window_size = 1024
        rms_values = []

        for i in range(0, len(audio) - window_size, window_size):
            window = audio[i:i + window_size]
            rms = np.sqrt(np.mean(window ** 2))
            rms_values.append(rms)

        if not rms_values:
            return np.sqrt(np.mean(audio ** 2))

        # Use low percentile as noise floor estimate
        return np.percentile(rms_values, percentile)

    def _cuda_available(self) -> bool:
        """Check if CUDA is available for PyTorch."""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            return False

    def analyze_noise(self, audio_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Analyze noise characteristics of an audio file without processing it.

        Parameters
        ----------
        audio_path : str or Path
            Path to audio file

        Returns
        -------
        dict
            Noise analysis results
        """
        audio_path = self.validate_input_path(audio_path)

        try:
            audio, sample_rate = sf.read(str(audio_path))

            # Convert to mono if stereo
            if len(audio.shape) > 1:
                audio = np.mean(audio, axis=1)

            # Calculate various noise metrics
            rms = np.sqrt(np.mean(audio ** 2))
            noise_floor = self._estimate_noise_floor(audio)
            snr_estimate = 20 * np.log10(rms / max(noise_floor, 1e-10))

            # Calculate spectral characteristics
            fft = np.fft.fft(audio)
            magnitude_spectrum = np.abs(fft)
            spectral_centroid = np.sum(np.arange(len(magnitude_spectrum)) * magnitude_spectrum) / np.sum(magnitude_spectrum)

            return {
                "file_path": str(audio_path),
                "sample_rate": sample_rate,
                "duration": len(audio) / sample_rate,
                "rms_level": rms,
                "noise_floor": noise_floor,
                "estimated_snr_db": snr_estimate,
                "spectral_centroid": spectral_centroid,
                "needs_denoising": snr_estimate < 20,  # Threshold for noisy audio
                "noise_level": "high" if snr_estimate < 10 else "medium" if snr_estimate < 20 else "low"
            }

        except Exception as e:
            error_msg = f"Failed to analyze noise for {audio_path.name}: {str(e)}"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg) from e

    def preview_denoising(self, audio_path: Union[str, Path], duration: float = 5.0) -> Dict[str, Any]:
        """
        Preview denoising effect on a short segment of audio.

        Parameters
        ----------
        audio_path : str or Path
            Path to audio file
        duration : float, default=5.0
            Duration of preview in seconds

        Returns
        -------
        dict
            Preview results with before/after statistics
        """
        audio_path = self.validate_input_path(audio_path)

        try:
            audio, sample_rate = sf.read(str(audio_path))

            # Convert to mono if stereo
            if len(audio.shape) > 1:
                audio = np.mean(audio, axis=1)

            # Extract preview segment
            preview_samples = int(duration * sample_rate)
            if len(audio) > preview_samples:
                # Take segment from middle of audio
                start_idx = (len(audio) - preview_samples) // 2
                audio_segment = audio[start_idx:start_idx + preview_samples]
            else:
                audio_segment = audio

            # Analyze before denoising
            noise_floor_before = self._estimate_noise_floor(audio_segment)

            # Apply denoising to preview segment
            denoised_segment = nr.reduce_noise(
                y=audio_segment,
                sr=sample_rate,
                prop_decrease=self.prop_decrease,
                stationary=self.stationary
            )

            # Analyze after denoising
            noise_floor_after = self._estimate_noise_floor(denoised_segment)
            noise_reduction_db = 20 * np.log10(noise_floor_before / max(noise_floor_after, 1e-10))

            return {
                "file_path": str(audio_path),
                "preview_duration": len(audio_segment) / sample_rate,
                "noise_floor_before": noise_floor_before,
                "noise_floor_after": noise_floor_after,
                "estimated_noise_reduction_db": noise_reduction_db,
                "prop_decrease": self.prop_decrease,
                "stationary": self.stationary
            }

        except Exception as e:
            error_msg = f"Failed to preview denoising for {audio_path.name}: {str(e)}"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg) from e
