"""
Audio normalization functionality.
"""

import logging
from typing import Dict, Any, <PERSON>, Optional
from pathlib import Path

from pydub import AudioSegment

from ..core.base import BaseProcessor
from ..core.exceptions import ProcessingError


class AudioNormalizer(BaseProcessor):
    """
    Audio normalization processor.

    Handles loudness normalization of audio files to target dBFS level.
    """

    def __init__(self, target_dbfs: float = -20.0, logger: Optional[logging.Logger] = None):
        """
        Initialize the audio normalizer.

        Parameters
        ----------
        target_dbfs : float, default=-20.0
            Target loudness level in dBFS (decibels relative to full scale)
        logger : logging.Logger, optional
            Logger instance
        """
        # Set attributes before calling super().__init__ so _validate_config can access them
        self.target_dbfs = target_dbfs

        config = {'target_dbfs': target_dbfs}
        super().__init__(config=config, logger=logger)

    def _validate_config(self) -> None:
        """Validate normalizer configuration."""
        if self.target_dbfs > 0:
            raise ValueError("Target dBFS must be negative or zero")
        if self.target_dbfs < -60:
            self.logger.warning(f"Target dBFS {self.target_dbfs} is very low, may result in very quiet audio")

    def process(self, input_path: Union[str, Path], output_path: Union[str, Path], **kwargs) -> Dict[str, Any]:
        """
        Normalize audio file to target dBFS level.

        Parameters
        ----------
        input_path : str or Path
            Path to input audio file
        output_path : str or Path
            Path to output normalized audio file
        **kwargs
            Additional processing parameters

        Returns
        -------
        dict
            Normalization results and metadata
        """
        input_path = self.validate_input_path(input_path)
        output_path = Path(output_path)
        self.ensure_output_dir(output_path.parent)

        try:
            # Load the audio file
            self.logger.debug(f"Loading audio file: {input_path}")
            audio = AudioSegment.from_file(str(input_path))

            # Get original loudness
            original_dbfs = audio.dBFS

            if original_dbfs == float('-inf'):
                self.logger.warning(f"Audio file {input_path.name} appears to be silent")
                # For silent audio, just copy the file
                audio.export(str(output_path), format="wav")

                return {
                    "input_file": str(input_path),
                    "output_file": str(output_path),
                    "original_dbfs": original_dbfs,
                    "target_dbfs": self.target_dbfs,
                    "final_dbfs": original_dbfs,
                    "gain_applied": 0.0,
                    "was_silent": True,
                    "success": True
                }

            self.logger.info(f"Original loudness: {original_dbfs:.2f} dBFS")

            # Calculate required gain adjustment
            gain_adjustment = self.target_dbfs - original_dbfs

            self.logger.debug(f"Applying gain adjustment: {gain_adjustment:.2f} dB")

            # Apply gain adjustment
            normalized_audio = audio.apply_gain(gain_adjustment)

            # Verify final loudness
            final_dbfs = normalized_audio.dBFS

            # Export normalized audio
            self.logger.debug(f"Exporting normalized audio to: {output_path}")
            normalized_audio.export(str(output_path), format="wav")

            self.logger.info(f"Normalized loudness: {final_dbfs:.2f} dBFS (target: {self.target_dbfs:.2f} dBFS)")

            return {
                "input_file": str(input_path),
                "output_file": str(output_path),
                "original_dbfs": original_dbfs,
                "target_dbfs": self.target_dbfs,
                "final_dbfs": final_dbfs,
                "gain_applied": gain_adjustment,
                "was_silent": False,
                "normalization_accuracy": abs(final_dbfs - self.target_dbfs),
                "success": True
            }

        except Exception as e:
            error_msg = f"Normalization failed for {input_path.name}: {str(e)}"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg) from e

    def analyze_loudness(self, audio_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Analyze loudness characteristics of an audio file without processing it.

        Parameters
        ----------
        audio_path : str or Path
            Path to audio file

        Returns
        -------
        dict
            Loudness analysis results
        """
        audio_path = self.validate_input_path(audio_path)

        try:
            audio = AudioSegment.from_file(str(audio_path))

            current_dbfs = audio.dBFS
            required_gain = self.target_dbfs - current_dbfs if current_dbfs != float('-inf') else 0

            return {
                "file_path": str(audio_path),
                "current_dbfs": current_dbfs,
                "target_dbfs": self.target_dbfs,
                "required_gain": required_gain,
                "is_silent": current_dbfs == float('-inf'),
                "needs_normalization": abs(required_gain) > 0.1,  # Threshold for meaningful change
                "would_amplify": required_gain > 0,
                "would_attenuate": required_gain < 0
            }

        except Exception as e:
            error_msg = f"Failed to analyze loudness for {audio_path.name}: {str(e)}"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg) from e

    def check_clipping_risk(self, audio_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Check if normalization would cause clipping.

        Parameters
        ----------
        audio_path : str or Path
            Path to audio file

        Returns
        -------
        dict
            Clipping risk analysis
        """
        try:
            analysis = self.analyze_loudness(audio_path)

            # Estimate peak level after normalization
            # This is a rough estimate since we don't have access to true peak
            estimated_peak_after_norm = analysis["current_dbfs"] + analysis["required_gain"]

            clipping_risk = estimated_peak_after_norm > -0.1  # Conservative threshold

            return {
                "file_path": str(audio_path),
                "current_dbfs": analysis["current_dbfs"],
                "required_gain": analysis["required_gain"],
                "estimated_peak_after_norm": estimated_peak_after_norm,
                "clipping_risk": clipping_risk,
                "recommended_target_dbfs": min(self.target_dbfs, analysis["current_dbfs"] - 0.5) if clipping_risk else self.target_dbfs
            }

        except Exception as e:
            error_msg = f"Failed to check clipping risk for {audio_path}: {str(e)}"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg) from e
