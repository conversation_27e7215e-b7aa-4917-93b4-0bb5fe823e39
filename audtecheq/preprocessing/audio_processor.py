"""
Main audio preprocessing class that combines resampling, normalization, and denoising.
"""

import os
from typing import Dict, Any, Union, Optional
from pathlib import Path

from ..core.base import BatchProcessor
from ..core.config import PreprocessingConfig
from ..core.exceptions import ProcessingError
from .resampler import AudioResampler
from .normalizer import AudioNormalizer
from .denoiser import AudioDenoiser


class AudioPreprocessor(BatchProcessor):
    """
    Complete audio preprocessing pipeline.
    
    Combines resampling, normalization, and noise reduction into a single processor.
    """
    
    def __init__(self, config: Optional[PreprocessingConfig] = None, **kwargs):
        """
        Initialize the audio preprocessor.
        
        Parameters
        ----------
        config : PreprocessingConfig, optional
            Preprocessing configuration
        **kwargs
            Additional arguments passed to base class
        """
        super().__init__(config=config.__dict__ if config else None, **kwargs)
        
        self.preprocessing_config = config or PreprocessingConfig()
        
        # Initialize sub-processors
        self.resampler = AudioResampler(
            target_sample_rate=self.preprocessing_config.target_sample_rate,
            target_channels=self.preprocessing_config.target_channels,
            logger=self.logger
        )
        
        self.normalizer = AudioNormalizer(
            target_dbfs=self.preprocessing_config.target_dbfs,
            logger=self.logger
        )
        
        self.denoiser = AudioDenoiser(
            prop_decrease=self.preprocessing_config.noise_reduction_prop,
            stationary=self.preprocessing_config.noise_reduction_stationary,
            logger=self.logger
        )
    
    def process(self, input_path: Union[str, Path], output_path: Union[str, Path], **kwargs) -> Dict[str, Any]:
        """
        Process a single audio file through the complete preprocessing pipeline.
        
        Parameters
        ----------
        input_path : str or Path
            Path to input audio file
        output_path : str or Path
            Path to output processed audio file
        **kwargs
            Additional processing parameters
            
        Returns
        -------
        dict
            Processing results and metadata
        """
        input_path = self.validate_input_path(input_path)
        output_path = Path(output_path)
        self.ensure_output_dir(output_path.parent)
        
        try:
            self.logger.info(f"Starting preprocessing of {input_path.name}")
            
            # Create temporary paths for intermediate steps
            temp_dir = output_path.parent / "temp"
            temp_dir.mkdir(exist_ok=True)
            
            base_name = input_path.stem
            resampled_path = temp_dir / f"{base_name}_resampled.wav"
            normalized_path = temp_dir / f"{base_name}_normalized.wav"
            
            # Step 1: Resample
            self.logger.info("Step 1/3: Resampling audio")
            resample_result = self.resampler.process(input_path, resampled_path)
            
            # Step 2: Normalize
            self.logger.info("Step 2/3: Normalizing loudness")
            normalize_result = self.normalizer.process(resampled_path, normalized_path)
            
            # Step 3: Denoise
            self.logger.info("Step 3/3: Reducing noise")
            denoise_result = self.denoiser.process(normalized_path, output_path)
            
            # Clean up temporary files
            if resampled_path.exists():
                resampled_path.unlink()
            if normalized_path.exists():
                normalized_path.unlink()
            if temp_dir.exists() and not any(temp_dir.iterdir()):
                temp_dir.rmdir()
            
            self.logger.info(f"Preprocessing completed: {output_path}")
            
            return {
                "input_file": str(input_path),
                "output_file": str(output_path),
                "steps_completed": ["resample", "normalize", "denoise"],
                "resample_result": resample_result,
                "normalize_result": normalize_result,
                "denoise_result": denoise_result,
                "success": True
            }
            
        except Exception as e:
            error_msg = f"Preprocessing failed for {input_path.name}: {str(e)}"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg) from e
    
    def process_step_by_step(self, input_path: Union[str, Path], output_dir: Union[str, Path], 
                           keep_intermediates: bool = False) -> Dict[str, Any]:
        """
        Process audio with separate output files for each step.
        
        Parameters
        ----------
        input_path : str or Path
            Path to input audio file
        output_dir : str or Path
            Directory to save all output files
        keep_intermediates : bool, default=False
            Whether to keep intermediate files
            
        Returns
        -------
        dict
            Processing results with paths to all output files
        """
        input_path = self.validate_input_path(input_path)
        output_dir = self.ensure_output_dir(output_dir)
        
        base_name = input_path.stem
        
        # Define output paths for each step
        resampled_path = output_dir / f"{base_name}_resampled.wav"
        normalized_path = output_dir / f"{base_name}_normalized.wav"
        denoised_path = output_dir / f"{base_name}_denoised.wav"
        
        try:
            # Step 1: Resample
            self.logger.info("Step 1/3: Resampling audio")
            resample_result = self.resampler.process(input_path, resampled_path)
            
            # Step 2: Normalize
            self.logger.info("Step 2/3: Normalizing loudness")
            normalize_result = self.normalizer.process(resampled_path, normalized_path)
            
            # Step 3: Denoise
            self.logger.info("Step 3/3: Reducing noise")
            denoise_result = self.denoiser.process(normalized_path, denoised_path)
            
            # Clean up intermediate files if not keeping them
            if not keep_intermediates:
                if resampled_path.exists():
                    resampled_path.unlink()
                if normalized_path.exists():
                    normalized_path.unlink()
            
            result = {
                "input_file": str(input_path),
                "output_files": {
                    "final": str(denoised_path)
                },
                "steps_completed": ["resample", "normalize", "denoise"],
                "resample_result": resample_result,
                "normalize_result": normalize_result,
                "denoise_result": denoise_result,
                "success": True
            }
            
            if keep_intermediates:
                result["output_files"].update({
                    "resampled": str(resampled_path),
                    "normalized": str(normalized_path)
                })
            
            return result
            
        except Exception as e:
            error_msg = f"Step-by-step preprocessing failed for {input_path.name}: {str(e)}"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg) from e
    
    def get_config_summary(self) -> Dict[str, Any]:
        """
        Get summary of current preprocessing configuration.
        
        Returns
        -------
        dict
            Configuration summary
        """
        return {
            "target_sample_rate": self.preprocessing_config.target_sample_rate,
            "target_channels": self.preprocessing_config.target_channels,
            "target_dbfs": self.preprocessing_config.target_dbfs,
            "noise_reduction_prop": self.preprocessing_config.noise_reduction_prop,
            "noise_reduction_stationary": self.preprocessing_config.noise_reduction_stationary
        }
