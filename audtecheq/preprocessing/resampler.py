"""
Audio resampling functionality.
"""

import logging
from typing import Dict, Any, Union, Optional
from pathlib import Path

from pydub import AudioSegment

from ..core.base import BaseProcessor
from ..core.exceptions import ProcessingError


class AudioResampler(BaseProcessor):
    """
    Audio resampling processor.
    
    Handles resampling audio files to target sample rate and channel configuration.
    """
    
    def __init__(self, target_sample_rate: int = 16000, target_channels: int = 1, 
                 logger: Optional[logging.Logger] = None):
        """
        Initialize the audio resampler.
        
        Parameters
        ----------
        target_sample_rate : int, default=16000
            Target sample rate in Hz
        target_channels : int, default=1
            Target number of channels (1 for mono, 2 for stereo)
        logger : logging.Logger, optional
            Logger instance
        """
        config = {
            'target_sample_rate': target_sample_rate,
            'target_channels': target_channels
        }
        super().__init__(config=config, logger=logger)
        
        self.target_sample_rate = target_sample_rate
        self.target_channels = target_channels
    
    def _validate_config(self) -> None:
        """Validate resampler configuration."""
        if self.target_sample_rate <= 0:
            raise ValueError("Target sample rate must be positive")
        if self.target_channels not in [1, 2]:
            raise ValueError("Target channels must be 1 (mono) or 2 (stereo)")
    
    def process(self, input_path: Union[str, Path], output_path: Union[str, Path], **kwargs) -> Dict[str, Any]:
        """
        Resample audio file to target sample rate and channel configuration.
        
        Parameters
        ----------
        input_path : str or Path
            Path to input audio file
        output_path : str or Path
            Path to output resampled audio file
        **kwargs
            Additional processing parameters
            
        Returns
        -------
        dict
            Resampling results and metadata
        """
        input_path = self.validate_input_path(input_path)
        output_path = Path(output_path)
        self.ensure_output_dir(output_path.parent)
        
        try:
            # Load the audio file
            self.logger.debug(f"Loading audio file: {input_path}")
            audio = AudioSegment.from_file(str(input_path))
            
            # Get original properties
            original_sample_rate = audio.frame_rate
            original_channels = audio.channels
            original_duration = len(audio) / 1000.0  # Duration in seconds
            
            self.logger.info(f"Original: {original_sample_rate}Hz, {original_channels} channels, {original_duration:.2f}s")
            
            # Resample to target sample rate
            if original_sample_rate != self.target_sample_rate:
                self.logger.debug(f"Resampling from {original_sample_rate}Hz to {self.target_sample_rate}Hz")
                audio = audio.set_frame_rate(self.target_sample_rate)
            
            # Convert to target channel configuration
            if original_channels != self.target_channels:
                self.logger.debug(f"Converting from {original_channels} to {self.target_channels} channels")
                audio = audio.set_channels(self.target_channels)
            
            # Export as WAV
            self.logger.debug(f"Exporting resampled audio to: {output_path}")
            audio.export(str(output_path), format="wav")
            
            # Calculate final properties
            final_duration = len(audio) / 1000.0
            
            self.logger.info(f"Resampled: {self.target_sample_rate}Hz, {self.target_channels} channels, {final_duration:.2f}s")
            
            return {
                "input_file": str(input_path),
                "output_file": str(output_path),
                "original_sample_rate": original_sample_rate,
                "target_sample_rate": self.target_sample_rate,
                "original_channels": original_channels,
                "target_channels": self.target_channels,
                "original_duration": original_duration,
                "final_duration": final_duration,
                "sample_rate_changed": original_sample_rate != self.target_sample_rate,
                "channels_changed": original_channels != self.target_channels,
                "success": True
            }
            
        except Exception as e:
            error_msg = f"Resampling failed for {input_path.name}: {str(e)}"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg) from e
    
    def get_audio_info(self, audio_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Get information about an audio file without processing it.
        
        Parameters
        ----------
        audio_path : str or Path
            Path to audio file
            
        Returns
        -------
        dict
            Audio file information
        """
        audio_path = self.validate_input_path(audio_path)
        
        try:
            audio = AudioSegment.from_file(str(audio_path))
            
            return {
                "file_path": str(audio_path),
                "sample_rate": audio.frame_rate,
                "channels": audio.channels,
                "duration_seconds": len(audio) / 1000.0,
                "duration_ms": len(audio),
                "sample_width": audio.sample_width,
                "max_possible_amplitude": audio.max_possible_amplitude,
                "needs_resampling": audio.frame_rate != self.target_sample_rate,
                "needs_channel_conversion": audio.channels != self.target_channels
            }
            
        except Exception as e:
            error_msg = f"Failed to get audio info for {audio_path.name}: {str(e)}"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg) from e
    
    def check_compatibility(self, audio_path: Union[str, Path]) -> bool:
        """
        Check if audio file is already in target format.
        
        Parameters
        ----------
        audio_path : str or Path
            Path to audio file
            
        Returns
        -------
        bool
            True if audio is already in target format
        """
        try:
            info = self.get_audio_info(audio_path)
            return not (info["needs_resampling"] or info["needs_channel_conversion"])
        except Exception:
            return False
