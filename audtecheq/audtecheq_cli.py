import argparse
import os
from pathlib import Path
from shutil import copytree
from audtecheq.utils.validation import validate_input_dir


# define parser to collect required inputs
def get_parser():

    __version__ = open(os.path.join(os.path.dirname(os.path.realpath(__file__)),
                                    '_version.py')).read()

    class MaxListAction(argparse.Action):
        def __call__(self, parser, namespace, values, option_string=None):
            if len(values) > 2:
                raise argparse.ArgumentError(self, "maximum length of list is 2")
            setattr(namespace, self.dest, values)

    parser = argparse.ArgumentParser(description='ADD DESCRIPTION')
    parser.add_argument('--skip_bids_validation', default=False,
                        help='Assume the input dataset is BIDS compliant and skip the validation \
                             (default: False).',
                        action="store_true")
    parser.add_argument('-v', '--version', action='version',
                        version='audtecheq version {}'.format(__version__))
    return parser


# define the CLI
def run_audtecheq():

    # get arguments from parser
    args = get_parser().parse_args()

    # special variable set in the container
    if os.getenv('IS_DOCKER'):
        exec_env = 'singularity'
        cgroup = Path('/proc/1/cgroup')
        if cgroup.exists() and 'docker' in cgroup.read_text():
            exec_env = 'docker'
    else:
        exec_env = 'local'

    # check if BIDS validation should be run
    if args.skip_bids_validation:
        print("Input data will not be checked for BIDS compliance.")
    else:
        print("Making sure the input data is BIDS compliant "
              "(warnings can be ignored in most cases).")
        validate_input_dir(exec_env, args.bids_dir)


# run the CLI
if __name__ == "__main__":

    run_audtecheq()
