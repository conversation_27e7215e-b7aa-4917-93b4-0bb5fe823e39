"""
Enhanced silence processing for AudioTechEquity pipeline.

This module provides functionality to discard long silences from audio
after VAD processing, as specified in the pipeline flow diagram.
"""

import json
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import torch
import torchaudio

from ..core.base import BaseProcessor
from ..core.config import VADConfig
from ..core.exceptions import ProcessingError


class SilenceProcessor(BaseProcessor):
    """
    Enhanced silence processor for discarding long silences.
    
    This processor works after VAD to remove long silence segments
    that exceed configurable thresholds, as shown in the pipeline diagram.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, **kwargs):
        """
        Initialize the silence processor.
        
        Parameters
        ----------
        config : dict, optional
            Silence processing configuration
        **kwargs
            Additional arguments passed to base class
        """
        super().__init__(config=config, **kwargs)
        
        # Default configuration
        self.config = config or {}
        self.max_silence_duration = self.config.get('max_silence_duration_sec', 2.0)
        self.min_speech_gap = self.config.get('min_speech_gap_sec', 0.1)
        self.silence_threshold = self.config.get('silence_threshold', 0.01)
        self.preserve_boundaries = self.config.get('preserve_boundaries', True)
        
    def process(self, input_path: Union[str, Path], output_path: Union[str, Path], 
                vad_timestamps_path: Optional[Union[str, Path]] = None, **kwargs) -> Dict[str, Any]:
        """
        Process audio to discard long silences.
        
        Parameters
        ----------
        input_path : str or Path
            Path to input audio file (typically VAD-processed)
        output_path : str or Path
            Path to output audio file
        vad_timestamps_path : str or Path, optional
            Path to VAD timestamps JSON file
        **kwargs
            Additional processing parameters:
            - max_silence_duration : float, default=2.0
                Maximum silence duration to keep (seconds)
            - preserve_boundaries : bool, default=True
                Whether to preserve small silences at segment boundaries
                
        Returns
        -------
        dict
            Processing results including statistics
        """
        input_path = self.validate_input_path(input_path)
        output_path = self.validate_output_path(output_path)
        
        # Override config with kwargs
        max_silence_duration = kwargs.get('max_silence_duration', self.max_silence_duration)
        preserve_boundaries = kwargs.get('preserve_boundaries', self.preserve_boundaries)
        
        try:
            self.logger.info(f"Processing silence removal for {input_path.name}")
            
            # Load audio
            waveform, sample_rate = torchaudio.load(str(input_path))
            
            # Convert to mono if stereo
            if waveform.shape[0] > 1:
                waveform = waveform.mean(dim=0, keepdim=True)
            
            # Load VAD timestamps if provided
            vad_segments = None
            if vad_timestamps_path:
                vad_segments = self._load_vad_timestamps(vad_timestamps_path)
            
            # Process silence removal
            processed_audio, processing_stats = self._remove_long_silences(
                waveform, sample_rate, vad_segments, max_silence_duration, preserve_boundaries
            )
            
            # Save processed audio
            torchaudio.save(str(output_path), processed_audio, sample_rate)
            
            # Calculate final statistics
            original_duration = waveform.shape[1] / sample_rate
            processed_duration = processed_audio.shape[1] / sample_rate
            
            results = {
                "input_file": str(input_path),
                "output_file": str(output_path),
                "original_duration": original_duration,
                "processed_duration": processed_duration,
                "silence_removed": original_duration - processed_duration,
                "compression_ratio": processed_duration / original_duration if original_duration > 0 else 0,
                "processing_stats": processing_stats,
                "config": {
                    "max_silence_duration": max_silence_duration,
                    "preserve_boundaries": preserve_boundaries,
                    "silence_threshold": self.silence_threshold
                },
                "success": True
            }
            
            self.logger.info(f"Silence processing completed: {processing_stats['long_silences_removed']} long silences removed")
            
            return results
            
        except Exception as e:
            error_msg = f"Silence processing failed for {input_path.name}: {str(e)}"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg) from e
    
    def _load_vad_timestamps(self, vad_path: Union[str, Path]) -> List[Dict[str, float]]:
        """
        Load VAD timestamps from JSON file.
        
        Parameters
        ----------
        vad_path : str or Path
            Path to VAD timestamps JSON file
            
        Returns
        -------
        list
            List of speech segments
        """
        try:
            with open(vad_path, 'r') as f:
                data = json.load(f)
                return data.get('speech_segments', [])
        except Exception as e:
            self.logger.warning(f"Failed to load VAD timestamps: {e}")
            return []
    
    def _remove_long_silences(self, waveform: torch.Tensor, sample_rate: int,
                             vad_segments: Optional[List[Dict[str, float]]], 
                             max_silence_duration: float, preserve_boundaries: bool) -> Tuple[torch.Tensor, Dict[str, Any]]:
        """
        Remove long silences from audio waveform.
        
        Parameters
        ----------
        waveform : torch.Tensor
            Input audio waveform
        sample_rate : int
            Sample rate
        vad_segments : list, optional
            VAD speech segments
        max_silence_duration : float
            Maximum silence duration to keep
        preserve_boundaries : bool
            Whether to preserve boundary silences
            
        Returns
        -------
        tuple
            Processed waveform and statistics
        """
        if vad_segments:
            # Use VAD segments to identify silences
            return self._remove_silences_with_vad(
                waveform, sample_rate, vad_segments, max_silence_duration, preserve_boundaries
            )
        else:
            # Use energy-based silence detection
            return self._remove_silences_energy_based(
                waveform, sample_rate, max_silence_duration, preserve_boundaries
            )
    
    def _remove_silences_with_vad(self, waveform: torch.Tensor, sample_rate: int,
                                 vad_segments: List[Dict[str, float]], max_silence_duration: float,
                                 preserve_boundaries: bool) -> Tuple[torch.Tensor, Dict[str, Any]]:
        """Remove silences using VAD segment information."""
        processed_segments = []
        long_silences_removed = 0
        total_silence_removed = 0.0
        
        # Sort segments by start time
        vad_segments = sorted(vad_segments, key=lambda x: x['start'])
        
        for i, segment in enumerate(vad_segments):
            start_sample = int(segment['start'] * sample_rate)
            end_sample = int(segment['end'] * sample_rate)
            
            # Add speech segment
            if end_sample > start_sample:
                speech_audio = waveform[:, start_sample:end_sample]
                processed_segments.append(speech_audio)
            
            # Handle silence after this segment
            if i < len(vad_segments) - 1:
                next_segment = vad_segments[i + 1]
                silence_duration = next_segment['start'] - segment['end']
                
                if silence_duration > max_silence_duration:
                    # Long silence - either remove or shorten
                    if preserve_boundaries:
                        # Keep a small amount of silence
                        keep_duration = min(max_silence_duration, 0.5)
                        silence_samples = int(keep_duration * sample_rate)
                        silence_start = end_sample
                        silence_audio = waveform[:, silence_start:silence_start + silence_samples]
                        processed_segments.append(silence_audio)
                    
                    long_silences_removed += 1
                    total_silence_removed += silence_duration - (0.5 if preserve_boundaries else 0)
                else:
                    # Short silence - keep it
                    silence_start = end_sample
                    silence_end = int(next_segment['start'] * sample_rate)
                    if silence_end > silence_start:
                        silence_audio = waveform[:, silence_start:silence_end]
                        processed_segments.append(silence_audio)
        
        # Concatenate all segments
        if processed_segments:
            processed_audio = torch.cat(processed_segments, dim=1)
        else:
            processed_audio = waveform
        
        stats = {
            "long_silences_removed": long_silences_removed,
            "total_silence_removed_sec": total_silence_removed,
            "segments_processed": len(vad_segments),
            "method": "vad_based"
        }
        
        return processed_audio, stats
    
    def _remove_silences_energy_based(self, waveform: torch.Tensor, sample_rate: int,
                                     max_silence_duration: float, preserve_boundaries: bool) -> Tuple[torch.Tensor, Dict[str, Any]]:
        """Remove silences using energy-based detection."""
        # Simple energy-based silence detection
        window_size = int(0.025 * sample_rate)  # 25ms windows
        hop_size = int(0.010 * sample_rate)     # 10ms hop
        
        # Calculate energy
        energy = []
        for i in range(0, waveform.shape[1] - window_size, hop_size):
            window = waveform[:, i:i + window_size]
            energy.append(torch.mean(window ** 2).item())
        
        # Determine silence threshold
        energy_threshold = max(np.percentile(energy, 20), self.silence_threshold)
        
        # Find silence regions
        silence_regions = []
        in_silence = False
        silence_start = 0
        
        for i, e in enumerate(energy):
            time = i * hop_size / sample_rate
            
            if e <= energy_threshold and not in_silence:
                in_silence = True
                silence_start = time
            elif e > energy_threshold and in_silence:
                in_silence = False
                silence_duration = time - silence_start
                if silence_duration > max_silence_duration:
                    silence_regions.append((silence_start, time, silence_duration))
        
        # Process audio to remove long silences
        if not silence_regions:
            return waveform, {"long_silences_removed": 0, "total_silence_removed_sec": 0.0, "method": "energy_based"}
        
        # Build segments to keep
        keep_segments = []
        last_end = 0.0
        total_removed = 0.0
        
        for silence_start, silence_end, duration in silence_regions:
            # Keep audio before silence
            if silence_start > last_end:
                start_sample = int(last_end * sample_rate)
                end_sample = int(silence_start * sample_rate)
                keep_segments.append(waveform[:, start_sample:end_sample])
            
            # Optionally keep short silence
            if preserve_boundaries:
                keep_duration = min(max_silence_duration, 0.5)
                silence_samples = int(keep_duration * sample_rate)
                start_sample = int(silence_start * sample_rate)
                keep_segments.append(waveform[:, start_sample:start_sample + silence_samples])
                total_removed += duration - keep_duration
            else:
                total_removed += duration
            
            last_end = silence_end
        
        # Keep remaining audio
        if last_end < waveform.shape[1] / sample_rate:
            start_sample = int(last_end * sample_rate)
            keep_segments.append(waveform[:, start_sample:])
        
        # Concatenate segments
        if keep_segments:
            processed_audio = torch.cat(keep_segments, dim=1)
        else:
            processed_audio = waveform
        
        stats = {
            "long_silences_removed": len(silence_regions),
            "total_silence_removed_sec": total_removed,
            "silence_regions": silence_regions,
            "method": "energy_based"
        }
        
        return processed_audio, stats
