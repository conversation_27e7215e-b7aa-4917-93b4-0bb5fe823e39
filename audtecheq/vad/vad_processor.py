"""
Voice Activity Detection processor using NeMo VAD models.
"""

import os
import json
import logging
from typing import Dict, Any, Union, Optional, List, Tuple
from pathlib import Path

import torch
import torchaudio
import numpy as np

from ..core.base import BaseProcessor
from ..core.config import VADConfig
from ..core.exceptions import ProcessingError, ModelLoadError


class VADProcessor(BaseProcessor):
    """
    Voice Activity Detection processor.
    
    Uses NeMo VAD models to detect speech segments and remove long silences.
    """
    
    def __init__(self, config: Optional[VADConfig] = None, **kwargs):
        """
        Initialize the VAD processor.
        
        Parameters
        ----------
        config : VADConfig, optional
            VAD configuration
        **kwargs
            Additional arguments passed to base class
        """
        super().__init__(config=config.__dict__ if config else None, **kwargs)
        
        self.vad_config = config or VADConfig()
        self.model = None
        self.device = self._get_device()
        
        # Load VAD model
        self._load_model()
    
    def _get_device(self) -> str:
        """Get appropriate device for processing."""
        if torch.cuda.is_available():
            return "cuda"
        return "cpu"
    
    def _load_model(self) -> None:
        """Load the VAD model."""
        try:
            # Try to import NeMo
            try:
                import nemo.collections.asr as nemo_asr
                self.logger.info(f"Loading VAD model: {self.vad_config.model_name}")
                
                if self.vad_config.model_name == "silero_vad":
                    # Use Silero VAD as fallback
                    self._load_silero_vad()
                else:
                    # Use NeMo VAD model
                    self.model = nemo_asr.models.EncDecClassificationModel.from_pretrained(
                        model_name=self.vad_config.model_name
                    )
                    self.model.to(self.device)
                    self.model.eval()
                
            except ImportError:
                self.logger.warning("NeMo not available, falling back to Silero VAD")
                self._load_silero_vad()
                
        except Exception as e:
            raise ModelLoadError(f"Failed to load VAD model: {str(e)}") from e
    
    def _load_silero_vad(self) -> None:
        """Load Silero VAD model as fallback."""
        try:
            import torch
            
            # Download Silero VAD model
            model, utils = torch.hub.load(
                repo_or_dir='snakers4/silero-vad',
                model='silero_vad',
                force_reload=False,
                onnx=False
            )
            
            self.model = model.to(self.device)
            self.model.eval()
            
            # Store utility functions
            self.get_speech_timestamps = utils[0]
            self.save_audio = utils[1]
            self.read_audio = utils[2]
            self.VADIterator = utils[3]
            self.collect_chunks = utils[4]
            
            self.logger.info("Silero VAD model loaded successfully")
            
        except Exception as e:
            raise ModelLoadError(f"Failed to load Silero VAD model: {str(e)}") from e
    
    def process(self, input_path: Union[str, Path], output_path: Union[str, Path], **kwargs) -> Dict[str, Any]:
        """
        Process audio file to detect speech segments and remove long silences.
        
        Parameters
        ----------
        input_path : str or Path
            Path to input audio file
        output_path : str or Path
            Path to output processed audio file
        **kwargs
            Additional processing parameters:
            - save_timestamps : bool, default=True
                Whether to save VAD timestamps to JSON file
            - min_speech_duration : float, optional
                Minimum speech duration in seconds
            - min_silence_duration : float, optional
                Minimum silence duration in seconds
            
        Returns
        -------
        dict
            VAD processing results and metadata
        """
        input_path = self.validate_input_path(input_path)
        output_path = Path(output_path)
        self.ensure_output_dir(output_path.parent)
        
        try:
            self.logger.info(f"Processing VAD for {input_path.name}")
            
            # Load audio
            waveform, sample_rate = torchaudio.load(str(input_path))
            
            # Convert to mono if stereo
            if waveform.shape[0] > 1:
                waveform = waveform.mean(dim=0, keepdim=True)
            
            # Resample to 16kHz if needed
            if sample_rate != 16000:
                resampler = torchaudio.transforms.Resample(orig_freq=sample_rate, new_freq=16000)
                waveform = resampler(waveform)
                sample_rate = 16000
            
            # Detect speech segments
            speech_timestamps = self._detect_speech_segments(waveform.squeeze(), sample_rate, **kwargs)
            
            # Process audio based on speech segments
            processed_audio, processing_stats = self._process_audio_segments(
                waveform, speech_timestamps, sample_rate, **kwargs
            )
            
            # Save processed audio
            torchaudio.save(str(output_path), processed_audio, sample_rate)
            
            # Save timestamps if requested
            save_timestamps = kwargs.get('save_timestamps', True)
            if save_timestamps:
                timestamps_path = output_path.with_suffix('.json')
                self._save_timestamps(speech_timestamps, timestamps_path)
            
            self.logger.info(f"VAD processing completed: {len(speech_timestamps)} speech segments detected")
            
            result = {
                "input_file": str(input_path),
                "output_file": str(output_path),
                "speech_segments": speech_timestamps,
                "num_segments": len(speech_timestamps),
                "original_duration": waveform.shape[1] / sample_rate,
                "processed_duration": processed_audio.shape[1] / sample_rate,
                "processing_stats": processing_stats,
                "sample_rate": sample_rate,
                "success": True
            }
            
            if save_timestamps:
                result["timestamps_file"] = str(timestamps_path)
            
            return result
            
        except Exception as e:
            error_msg = f"VAD processing failed for {input_path.name}: {str(e)}"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg) from e
    
    def _detect_speech_segments(self, waveform: torch.Tensor, sample_rate: int, **kwargs) -> List[Dict[str, float]]:
        """
        Detect speech segments in audio waveform.
        
        Parameters
        ----------
        waveform : torch.Tensor
            Audio waveform (1D tensor)
        sample_rate : int
            Sample rate of audio
        **kwargs
            Additional parameters
            
        Returns
        -------
        list
            List of speech segments with start and end times
        """
        try:
            if hasattr(self, 'get_speech_timestamps'):
                # Use Silero VAD
                speech_timestamps = self.get_speech_timestamps(
                    waveform,
                    self.model,
                    threshold=self.vad_config.threshold,
                    sampling_rate=sample_rate,
                    min_speech_duration_ms=kwargs.get('min_speech_duration', self.vad_config.min_speech_duration_ms),
                    min_silence_duration_ms=kwargs.get('min_silence_duration', self.vad_config.min_silence_duration_ms),
                    window_size_samples=self.vad_config.window_size_samples,
                    speech_pad_ms=self.vad_config.speech_pad_ms
                )
                
                # Convert to our format
                segments = []
                for segment in speech_timestamps:
                    segments.append({
                        'start': segment['start'] / sample_rate,
                        'end': segment['end'] / sample_rate,
                        'confidence': 1.0  # Silero doesn't provide confidence scores
                    })
                
                return segments
            
            else:
                # Use NeMo VAD (placeholder implementation)
                # This would need to be implemented based on specific NeMo VAD model
                self.logger.warning("NeMo VAD implementation not yet available, using simple energy-based VAD")
                return self._simple_energy_vad(waveform, sample_rate, **kwargs)
                
        except Exception as e:
            self.logger.error(f"Speech detection failed: {str(e)}")
            # Fallback to simple energy-based VAD
            return self._simple_energy_vad(waveform, sample_rate, **kwargs)
    
    def _simple_energy_vad(self, waveform: torch.Tensor, sample_rate: int, **kwargs) -> List[Dict[str, float]]:
        """
        Simple energy-based VAD as fallback.
        
        Parameters
        ----------
        waveform : torch.Tensor
            Audio waveform
        sample_rate : int
            Sample rate
        **kwargs
            Additional parameters
            
        Returns
        -------
        list
            List of speech segments
        """
        # Calculate energy in windows
        window_size = int(0.025 * sample_rate)  # 25ms windows
        hop_size = int(0.010 * sample_rate)     # 10ms hop
        
        energy = []
        for i in range(0, len(waveform) - window_size, hop_size):
            window = waveform[i:i + window_size]
            energy.append(torch.mean(window ** 2).item())
        
        # Threshold based on percentile
        energy_threshold = np.percentile(energy, 70)
        
        # Find speech segments
        segments = []
        in_speech = False
        start_time = 0
        
        for i, e in enumerate(energy):
            time = i * hop_size / sample_rate
            
            if e > energy_threshold and not in_speech:
                # Start of speech
                in_speech = True
                start_time = time
            elif e <= energy_threshold and in_speech:
                # End of speech
                in_speech = False
                if time - start_time > 0.1:  # Minimum 100ms
                    segments.append({
                        'start': start_time,
                        'end': time,
                        'confidence': min(e / energy_threshold, 1.0)
                    })
        
        # Handle case where audio ends during speech
        if in_speech:
            segments.append({
                'start': start_time,
                'end': len(waveform) / sample_rate,
                'confidence': 0.8
            })
        
        return segments
    
    def _process_audio_segments(self, waveform: torch.Tensor, speech_segments: List[Dict[str, float]], 
                              sample_rate: int, **kwargs) -> Tuple[torch.Tensor, Dict[str, Any]]:
        """
        Process audio based on detected speech segments.
        
        Parameters
        ----------
        waveform : torch.Tensor
            Original audio waveform
        speech_segments : list
            List of speech segments
        sample_rate : int
            Sample rate
        **kwargs
            Additional parameters
            
        Returns
        -------
        tuple
            Processed audio waveform and processing statistics
        """
        if not speech_segments:
            self.logger.warning("No speech segments detected, returning original audio")
            return waveform, {"segments_removed": 0, "silence_removed_seconds": 0}
        
        # Extract speech segments
        processed_segments = []
        total_silence_removed = 0
        
        for segment in speech_segments:
            start_sample = int(segment['start'] * sample_rate)
            end_sample = int(segment['end'] * sample_rate)
            
            # Ensure bounds are valid
            start_sample = max(0, start_sample)
            end_sample = min(waveform.shape[1], end_sample)
            
            if end_sample > start_sample:
                segment_audio = waveform[:, start_sample:end_sample]
                processed_segments.append(segment_audio)
        
        if processed_segments:
            # Concatenate all speech segments
            processed_audio = torch.cat(processed_segments, dim=1)
            
            # Calculate statistics
            original_duration = waveform.shape[1] / sample_rate
            processed_duration = processed_audio.shape[1] / sample_rate
            silence_removed = original_duration - processed_duration
            
            stats = {
                "segments_kept": len(processed_segments),
                "segments_removed": 0,  # We don't explicitly remove segments, just keep speech
                "silence_removed_seconds": silence_removed,
                "compression_ratio": processed_duration / original_duration if original_duration > 0 else 0
            }
            
            return processed_audio, stats
        else:
            # No valid segments found
            return waveform, {"segments_removed": 0, "silence_removed_seconds": 0}
    
    def _save_timestamps(self, speech_segments: List[Dict[str, float]], output_path: Path) -> None:
        """
        Save speech timestamps to JSON file.
        
        Parameters
        ----------
        speech_segments : list
            List of speech segments
        output_path : Path
            Output path for JSON file
        """
        try:
            with open(output_path, 'w') as f:
                json.dump({
                    "speech_segments": speech_segments,
                    "vad_config": {
                        "model_name": self.vad_config.model_name,
                        "threshold": self.vad_config.threshold,
                        "min_speech_duration_ms": self.vad_config.min_speech_duration_ms,
                        "min_silence_duration_ms": self.vad_config.min_silence_duration_ms
                    }
                }, f, indent=2)
            
            self.logger.debug(f"VAD timestamps saved to {output_path}")
            
        except Exception as e:
            self.logger.warning(f"Failed to save VAD timestamps: {str(e)}")
    
    def analyze_speech_activity(self, audio_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Analyze speech activity in audio file without processing it.
        
        Parameters
        ----------
        audio_path : str or Path
            Path to audio file
            
        Returns
        -------
        dict
            Speech activity analysis results
        """
        audio_path = self.validate_input_path(audio_path)
        
        try:
            # Load audio
            waveform, sample_rate = torchaudio.load(str(audio_path))
            
            # Convert to mono if stereo
            if waveform.shape[0] > 1:
                waveform = waveform.mean(dim=0, keepdim=True)
            
            # Resample to 16kHz if needed
            if sample_rate != 16000:
                resampler = torchaudio.transforms.Resample(orig_freq=sample_rate, new_freq=16000)
                waveform = resampler(waveform)
                sample_rate = 16000
            
            # Detect speech segments
            speech_segments = self._detect_speech_segments(waveform.squeeze(), sample_rate)
            
            # Calculate statistics
            total_duration = waveform.shape[1] / sample_rate
            speech_duration = sum(seg['end'] - seg['start'] for seg in speech_segments)
            silence_duration = total_duration - speech_duration
            
            return {
                "file_path": str(audio_path),
                "total_duration": total_duration,
                "speech_duration": speech_duration,
                "silence_duration": silence_duration,
                "speech_ratio": speech_duration / total_duration if total_duration > 0 else 0,
                "num_speech_segments": len(speech_segments),
                "avg_segment_duration": speech_duration / len(speech_segments) if speech_segments else 0,
                "speech_segments": speech_segments
            }
            
        except Exception as e:
            error_msg = f"Failed to analyze speech activity for {audio_path.name}: {str(e)}"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg) from e
