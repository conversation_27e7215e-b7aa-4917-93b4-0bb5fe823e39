"""
Metrics calculation for ASR and diarization evaluation.
"""

import logging
from typing import List, Optional
import re


class MetricsCalculator:
    """
    Calculator for various evaluation metrics including WER, CER, and diarization metrics.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        Initialize metrics calculator.
        
        Parameters
        ----------
        logger : logging.Logger, optional
            Logger instance
        """
        self.logger = logger or logging.getLogger(__name__)
    
    def calculate_wer(self, reference: str, hypothesis: str) -> float:
        """
        Calculate Word Error Rate (WER).
        
        Parameters
        ----------
        reference : str
            Reference (ground truth) text
        hypothesis : str
            Hypothesis (predicted) text
            
        Returns
        -------
        float
            WER score (0.0 = perfect, higher = worse)
        """
        # Normalize text
        ref_words = self._normalize_text(reference).split()
        hyp_words = self._normalize_text(hypothesis).split()
        
        # Calculate edit distance
        edit_distance = self._calculate_edit_distance(ref_words, hyp_words)
        
        # Calculate WER
        if len(ref_words) == 0:
            return 0.0 if len(hyp_words) == 0 else float('inf')
        
        wer = edit_distance / len(ref_words)
        return wer
    
    def calculate_cer(self, reference: str, hypothesis: str) -> float:
        """
        Calculate Character Error Rate (CER).
        
        Parameters
        ----------
        reference : str
            Reference (ground truth) text
        hypothesis : str
            Hypothesis (predicted) text
            
        Returns
        -------
        float
            CER score (0.0 = perfect, higher = worse)
        """
        # Normalize text
        ref_chars = list(self._normalize_text(reference))
        hyp_chars = list(self._normalize_text(hypothesis))
        
        # Calculate edit distance
        edit_distance = self._calculate_edit_distance(ref_chars, hyp_chars)
        
        # Calculate CER
        if len(ref_chars) == 0:
            return 0.0 if len(hyp_chars) == 0 else float('inf')
        
        cer = edit_distance / len(ref_chars)
        return cer
    
    def _normalize_text(self, text: str) -> str:
        """
        Normalize text for evaluation.
        
        Parameters
        ----------
        text : str
            Input text
            
        Returns
        -------
        str
            Normalized text
        """
        # Convert to lowercase
        text = text.lower()
        
        # Remove punctuation
        text = re.sub(r'[^\w\s]', '', text)
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def _calculate_edit_distance(self, seq1: List, seq2: List) -> int:
        """
        Calculate Levenshtein edit distance between two sequences.
        
        Parameters
        ----------
        seq1 : list
            First sequence
        seq2 : list
            Second sequence
            
        Returns
        -------
        int
            Edit distance
        """
        len1, len2 = len(seq1), len(seq2)
        
        # Create matrix
        matrix = [[0] * (len2 + 1) for _ in range(len1 + 1)]
        
        # Initialize first row and column
        for i in range(len1 + 1):
            matrix[i][0] = i
        for j in range(len2 + 1):
            matrix[0][j] = j
        
        # Fill matrix
        for i in range(1, len1 + 1):
            for j in range(1, len2 + 1):
                if seq1[i-1] == seq2[j-1]:
                    cost = 0
                else:
                    cost = 1
                
                matrix[i][j] = min(
                    matrix[i-1][j] + 1,      # deletion
                    matrix[i][j-1] + 1,      # insertion
                    matrix[i-1][j-1] + cost  # substitution
                )
        
        return matrix[len1][len2]
    
    def calculate_accuracy(self, reference: str, hypothesis: str) -> float:
        """
        Calculate word-level accuracy.
        
        Parameters
        ----------
        reference : str
            Reference text
        hypothesis : str
            Hypothesis text
            
        Returns
        -------
        float
            Accuracy score (0.0-1.0)
        """
        wer = self.calculate_wer(reference, hypothesis)
        return max(0.0, 1.0 - wer)
    
    def calculate_precision_recall_f1(self, reference_words: List[str], 
                                    hypothesis_words: List[str]) -> dict:
        """
        Calculate precision, recall, and F1 score for word recognition.
        
        Parameters
        ----------
        reference_words : list
            Reference words
        hypothesis_words : list
            Hypothesis words
            
        Returns
        -------
        dict
            Dictionary with precision, recall, and f1 scores
        """
        ref_set = set(reference_words)
        hyp_set = set(hypothesis_words)
        
        true_positives = len(ref_set & hyp_set)
        false_positives = len(hyp_set - ref_set)
        false_negatives = len(ref_set - hyp_set)
        
        precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0.0
        recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0.0
        f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0
        
        return {
            "precision": precision,
            "recall": recall,
            "f1": f1,
            "true_positives": true_positives,
            "false_positives": false_positives,
            "false_negatives": false_negatives
        }
    
    def calculate_diarization_error_rate(self, reference_segments: List[dict], 
                                       hypothesis_segments: List[dict]) -> float:
        """
        Calculate Diarization Error Rate (DER).
        
        Parameters
        ----------
        reference_segments : list
            Reference diarization segments
        hypothesis_segments : list
            Hypothesis diarization segments
            
        Returns
        -------
        float
            DER score
        """
        # This is a simplified DER calculation
        # In practice, you would use specialized libraries like pyannote.metrics
        
        # For now, return a placeholder
        self.logger.warning("DER calculation not fully implemented")
        return 0.0
    
    def calculate_speaker_purity(self, segments: List[dict]) -> dict:
        """
        Calculate speaker purity metrics.
        
        Parameters
        ----------
        segments : list
            Diarization segments
            
        Returns
        -------
        dict
            Purity metrics
        """
        if not segments:
            return {"purity": 0.0, "coverage": 0.0}
        
        # Calculate basic purity metrics
        speakers = set(seg['speaker'] for seg in segments)
        total_duration = sum(seg['duration'] for seg in segments)
        
        speaker_durations = {}
        for speaker in speakers:
            speaker_segments = [seg for seg in segments if seg['speaker'] == speaker]
            speaker_durations[speaker] = sum(seg['duration'] for seg in speaker_segments)
        
        # Calculate purity (simplified)
        max_speaker_duration = max(speaker_durations.values()) if speaker_durations else 0
        purity = max_speaker_duration / total_duration if total_duration > 0 else 0
        
        return {
            "purity": purity,
            "coverage": len(speakers),
            "speaker_durations": speaker_durations,
            "total_duration": total_duration
        }
