"""
Enhanced diarization testing with specific metrics from the pipeline flow diagram.

This module implements the detailed testing components shown in the flow diagram:
- Speaker count sanity checks (flagging if #speakers > n)
- Cluster evaluation based on cosine distance (intra-cluster vs inter-cluster)
"""

import json
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
from sklearn.metrics.pairwise import cosine_distances

from ..core.base import BaseProcessor
from ..core.exceptions import ProcessingError
from ..core.data_manager import DataManager
from .metrics_calculator import MetricsCalculator


class EnhancedDiarizationTester(BaseProcessor):
    """
    Enhanced diarization tester implementing specific metrics from the flow diagram.

    This tester provides:
    1. Speaker count sanity checks with configurable thresholds
    2. Cluster evaluation using cosine distance metrics
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None, **kwargs):
        """
        Initialize the enhanced diarization tester.

        Parameters
        ----------
        config : dict, optional
            Testing configuration
        **kwargs
            Additional arguments passed to base class
        """
        super().__init__(config=config, **kwargs)

        # Default configuration
        self.config = config or {}
        self.max_speakers_threshold = self.config.get('max_speakers_threshold', 6)
        self.cosine_distance_threshold = self.config.get('cosine_distance_threshold', 0.3)

        self.metrics_calculator = MetricsCalculator(logger=self.logger)
        self.data_manager = DataManager()

    def process(self, input_path: Union[str, Path], output_path: Union[str, Path],
                embeddings_path: Optional[Union[str, Path]] = None, **kwargs) -> Dict[str, Any]:
        """
        Process diarization results with enhanced testing metrics.

        Parameters
        ----------
        input_path : str or Path
            Path to diarization results directory or file
        output_path : str or Path
            Path to output evaluation report
        embeddings_path : str or Path, optional
            Path to speaker embeddings for cluster analysis
        **kwargs
            Additional testing parameters:
            - expected_speakers : int, optional
                Expected number of speakers for validation
            - max_speakers_threshold : int, default=6
                Maximum reasonable number of speakers
            - enable_cluster_analysis : bool, default=True
                Whether to perform cluster analysis

        Returns
        -------
        dict
            Enhanced testing results with flags and detailed metrics
        """
        input_path = self.validate_input_path(input_path)
        output_path = self.validate_output_path(output_path)

        # Override config with kwargs
        max_speakers = kwargs.get('max_speakers_threshold', self.max_speakers_threshold)
        expected_speakers = kwargs.get('expected_speakers')
        enable_cluster_analysis = kwargs.get('enable_cluster_analysis', True)

        try:
            self.logger.info(f"Enhanced diarization testing for {input_path}")

            # Load diarization data
            diarization_data = self._load_diarization_data(input_path)

            if not diarization_data:
                raise ProcessingError("No valid diarization data found")

            # Perform enhanced testing
            test_results = {
                "input_path": str(input_path),
                "output_path": str(output_path),
                "test_config": {
                    "max_speakers_threshold": max_speakers,
                    "expected_speakers": expected_speakers,
                    "enable_cluster_analysis": enable_cluster_analysis
                },
                "tests": {},
                "flags": {},
                "summary": {}
            }

            # Test 1: Speaker Count Sanity Checks
            self.logger.info("Performing speaker count sanity checks...")
            speaker_count_results = self._test_speaker_count_sanity(
                diarization_data, max_speakers, expected_speakers
            )
            test_results["tests"]["speaker_count_sanity"] = speaker_count_results
            test_results["flags"]["speaker_count_flags"] = speaker_count_results.get("flags", [])

            # Test 2: Cluster Evaluation (if embeddings available)
            if enable_cluster_analysis and embeddings_path:
                self.logger.info("Performing cluster evaluation...")
                cluster_results = self._test_cluster_evaluation(
                    diarization_data, embeddings_path
                )
                test_results["tests"]["cluster_evaluation"] = cluster_results
                test_results["flags"]["cluster_flags"] = cluster_results.get("flags", [])
            elif enable_cluster_analysis:
                self.logger.warning("Cluster analysis requested but no embeddings path provided")
                test_results["tests"]["cluster_evaluation"] = {"status": "skipped", "reason": "no_embeddings"}

            # Generate summary
            test_results["summary"] = self._generate_enhanced_summary(test_results)

            # Save results using data manager
            saved_path = self.data_manager.save_test_results(
                test_results,
                "enhanced_diarization",
                output_path.name if output_path else None
            )
            test_results["output_path"] = str(saved_path)

            self.logger.info(f"Enhanced diarization testing completed")
            self.logger.info(f"Total flags raised: {sum(len(flags) for flags in test_results['flags'].values())}")

            return test_results

        except Exception as e:
            error_msg = f"Enhanced diarization testing failed: {str(e)}"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg) from e

    def _load_diarization_data(self, input_path: Path) -> List[Dict[str, Any]]:
        """Load diarization data from various formats."""
        diarization_data = []

        if input_path.is_file():
            # Single file
            data = self._load_single_diarization_file(input_path)
            if data:
                diarization_data.append(data)
        elif input_path.is_dir():
            # Directory with multiple files
            for file_path in input_path.rglob("*"):
                if file_path.suffix in ['.csv', '.json', '.rttm']:
                    data = self._load_single_diarization_file(file_path)
                    if data:
                        diarization_data.append(data)

        return diarization_data

    def _load_single_diarization_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """Load a single diarization file."""
        try:
            if file_path.suffix == '.csv':
                import pandas as pd
                df = pd.read_csv(file_path)
                segments = df.to_dict('records')
                return {
                    'file_path': str(file_path),
                    'segments': segments,
                    'format': 'csv'
                }

            elif file_path.suffix == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                return {
                    'file_path': str(file_path),
                    'segments': data.get('segments', []),
                    'format': 'json'
                }

            elif file_path.suffix == '.rttm':
                segments = self._parse_rttm_file(file_path)
                return {
                    'file_path': str(file_path),
                    'segments': segments,
                    'format': 'rttm'
                }

        except Exception as e:
            self.logger.warning(f"Failed to load diarization file {file_path}: {e}")
            return None

    def _parse_rttm_file(self, file_path: Path) -> List[Dict[str, Any]]:
        """Parse RTTM format diarization file."""
        segments = []

        with open(file_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    parts = line.split()
                    if len(parts) >= 8 and parts[0] == 'SPEAKER':
                        start_time = float(parts[3])
                        duration = float(parts[4])
                        end_time = start_time + duration
                        speaker_id = parts[7]

                        segments.append({
                            'start_time': start_time,
                            'end_time': end_time,
                            'duration': duration,
                            'speaker': speaker_id
                        })

        return segments

    def _test_speaker_count_sanity(self, diarization_data: List[Dict[str, Any]],
                                  max_speakers: int, expected_speakers: Optional[int]) -> Dict[str, Any]:
        """
        Test speaker count sanity - Flag if #speakers > n.

        This implements the "Speaker count sanity checks" from the flow diagram.
        """
        results = {
            "test_name": "Speaker Count Sanity Checks",
            "description": "Flag files with unreasonable number of speakers",
            "max_speakers_threshold": max_speakers,
            "expected_speakers": expected_speakers,
            "files_tested": len(diarization_data),
            "flags": [],
            "statistics": {}
        }

        speaker_counts = []
        flagged_files = []

        for data in diarization_data:
            segments = data['segments']
            speakers = set(seg.get('speaker', 'unknown') for seg in segments)
            speaker_count = len(speakers)
            speaker_counts.append(speaker_count)

            file_analysis = {
                'file': data['file_path'],
                'detected_speakers': speaker_count,
                'speakers': list(speakers)
            }

            # Flag 1: Too many speakers
            if speaker_count > max_speakers:
                flag = {
                    'type': 'excessive_speakers',
                    'severity': 'high',
                    'file': data['file_path'],
                    'detected_speakers': speaker_count,
                    'threshold': max_speakers,
                    'message': f"Detected {speaker_count} speakers, exceeds threshold of {max_speakers}"
                }
                results["flags"].append(flag)
                flagged_files.append(file_analysis)

            # Flag 2: Mismatch with expected speakers
            if expected_speakers is not None and speaker_count != expected_speakers:
                severity = 'high' if abs(speaker_count - expected_speakers) > 1 else 'medium'
                flag = {
                    'type': 'speaker_count_mismatch',
                    'severity': severity,
                    'file': data['file_path'],
                    'detected_speakers': speaker_count,
                    'expected_speakers': expected_speakers,
                    'difference': speaker_count - expected_speakers,
                    'message': f"Expected {expected_speakers} speakers, detected {speaker_count}"
                }
                results["flags"].append(flag)
                if file_analysis not in flagged_files:
                    flagged_files.append(file_analysis)

        # Calculate statistics
        results["statistics"] = {
            "total_files": len(diarization_data),
            "flagged_files": len(flagged_files),
            "flag_rate": len(flagged_files) / len(diarization_data) if diarization_data else 0,
            "speaker_count_distribution": {
                "min": min(speaker_counts) if speaker_counts else 0,
                "max": max(speaker_counts) if speaker_counts else 0,
                "mean": np.mean(speaker_counts) if speaker_counts else 0,
                "std": np.std(speaker_counts) if speaker_counts else 0
            },
            "files_exceeding_threshold": sum(1 for count in speaker_counts if count > max_speakers),
            "flagged_file_details": flagged_files
        }

        return results



    def _test_cluster_evaluation(self, diarization_data: List[Dict[str, Any]],
                               embeddings_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Test cluster evaluation based on cosine distance.

        This implements the "Evaluate clusters based on cosine distance" from the flow diagram.
        Analyzes intra-cluster vs inter-cluster distances.
        """
        results = {
            "test_name": "Cluster Evaluation",
            "description": "Evaluate speaker clusters using cosine distance metrics",
            "embeddings_path": str(embeddings_path),
            "files_tested": len(diarization_data),
            "flags": [],
            "statistics": {}
        }

        try:
            # Load speaker embeddings
            embeddings_data = self._load_speaker_embeddings(embeddings_path)

            if not embeddings_data:
                results["error"] = "Failed to load speaker embeddings"
                return results

            cluster_metrics = []

            for data in diarization_data:
                file_path = data['file_path']
                segments = data['segments']

                # Get embeddings for this file
                file_embeddings = embeddings_data.get(file_path, {})

                if not file_embeddings:
                    self.logger.warning(f"No embeddings found for {file_path}")
                    continue

                # Analyze clusters for this file
                file_cluster_metrics = self._analyze_file_clusters(
                    segments, file_embeddings, file_path
                )

                if file_cluster_metrics:
                    cluster_metrics.append(file_cluster_metrics)

                    # Check for cluster quality flags
                    flags = self._check_cluster_quality_flags(file_cluster_metrics)
                    results["flags"].extend(flags)

            # Calculate overall statistics
            if cluster_metrics:
                results["statistics"] = self._calculate_cluster_statistics(cluster_metrics)
            else:
                results["statistics"] = {"error": "No valid cluster metrics calculated"}

        except Exception as e:
            results["error"] = f"Cluster evaluation failed: {str(e)}"
            self.logger.error(f"Cluster evaluation error: {e}")

        return results

    def _load_speaker_embeddings(self, embeddings_path: Union[str, Path]) -> Dict[str, Any]:
        """Load speaker embeddings from file."""
        embeddings_path = Path(embeddings_path)

        try:
            if embeddings_path.suffix == '.json':
                with open(embeddings_path, 'r') as f:
                    return json.load(f)
            elif embeddings_path.suffix in ['.npy', '.npz']:
                # Handle numpy arrays
                data = np.load(embeddings_path, allow_pickle=True)
                if isinstance(data, np.ndarray):
                    return {"embeddings": data.item()}
                else:
                    return dict(data)
            else:
                self.logger.warning(f"Unsupported embeddings format: {embeddings_path.suffix}")
                return {}
        except Exception as e:
            self.logger.error(f"Failed to load embeddings: {e}")
            return {}

    def _analyze_file_clusters(self, segments: List[Dict[str, Any]],
                              embeddings: Dict[str, Any], file_path: str) -> Optional[Dict[str, Any]]:
        """Analyze clusters for a single file."""
        try:
            # Group segments by speaker
            speaker_segments = {}
            for segment in segments:
                speaker = segment.get('speaker', 'unknown')
                if speaker not in speaker_segments:
                    speaker_segments[speaker] = []
                speaker_segments[speaker].append(segment)

            # Get embeddings for each speaker
            speaker_embeddings = {}
            for speaker in speaker_segments:
                if speaker in embeddings:
                    speaker_embeddings[speaker] = np.array(embeddings[speaker])

            if len(speaker_embeddings) < 2:
                return None  # Need at least 2 speakers for cluster analysis

            # Calculate intra-cluster and inter-cluster distances
            intra_distances = []
            inter_distances = []

            speakers = list(speaker_embeddings.keys())

            # Intra-cluster distances (within same speaker)
            for speaker, embedding_matrix in speaker_embeddings.items():
                if embedding_matrix.ndim == 2 and embedding_matrix.shape[0] > 1:
                    # Multiple embeddings for this speaker
                    distances = cosine_distances(embedding_matrix)
                    # Get upper triangle (excluding diagonal)
                    mask = np.triu(np.ones_like(distances, dtype=bool), k=1)
                    intra_distances.extend(distances[mask])

            # Inter-cluster distances (between different speakers)
            for i in range(len(speakers)):
                for j in range(i + 1, len(speakers)):
                    speaker1, speaker2 = speakers[i], speakers[j]
                    emb1 = speaker_embeddings[speaker1]
                    emb2 = speaker_embeddings[speaker2]

                    # Ensure 2D arrays
                    if emb1.ndim == 1:
                        emb1 = emb1.reshape(1, -1)
                    if emb2.ndim == 1:
                        emb2 = emb2.reshape(1, -1)

                    distances = cosine_distances(emb1, emb2)
                    inter_distances.extend(distances.flatten())

            # Calculate metrics
            metrics = {
                "file_path": file_path,
                "num_speakers": len(speaker_embeddings),
                "intra_cluster_distances": {
                    "mean": np.mean(intra_distances) if intra_distances else 0,
                    "std": np.std(intra_distances) if intra_distances else 0,
                    "min": np.min(intra_distances) if intra_distances else 0,
                    "max": np.max(intra_distances) if intra_distances else 0,
                    "count": len(intra_distances)
                },
                "inter_cluster_distances": {
                    "mean": np.mean(inter_distances) if inter_distances else 0,
                    "std": np.std(inter_distances) if inter_distances else 0,
                    "min": np.min(inter_distances) if inter_distances else 0,
                    "max": np.max(inter_distances) if inter_distances else 0,
                    "count": len(inter_distances)
                }
            }

            # Calculate cluster separation quality
            if intra_distances and inter_distances:
                mean_intra = np.mean(intra_distances)
                mean_inter = np.mean(inter_distances)

                # Silhouette-like score
                separation_score = (mean_inter - mean_intra) / max(mean_inter, mean_intra)
                metrics["separation_score"] = separation_score

                # Cluster quality assessment
                if separation_score > 0.3:
                    metrics["cluster_quality"] = "good"
                elif separation_score > 0.1:
                    metrics["cluster_quality"] = "fair"
                else:
                    metrics["cluster_quality"] = "poor"

            return metrics

        except Exception as e:
            self.logger.error(f"Cluster analysis failed for {file_path}: {e}")
            return None

    def _check_cluster_quality_flags(self, cluster_metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check for cluster quality issues and generate flags."""
        flags = []
        file_path = cluster_metrics["file_path"]

        # Flag 1: Poor cluster separation
        separation_score = cluster_metrics.get("separation_score", 0)
        if separation_score < 0.1:
            flags.append({
                'type': 'poor_cluster_separation',
                'severity': 'high',
                'file': file_path,
                'separation_score': separation_score,
                'threshold': 0.1,
                'message': f"Poor cluster separation (score: {separation_score:.3f})"
            })

        # Flag 2: High intra-cluster distance
        intra_mean = cluster_metrics["intra_cluster_distances"]["mean"]
        if intra_mean > self.cosine_distance_threshold:
            flags.append({
                'type': 'high_intra_cluster_distance',
                'severity': 'medium',
                'file': file_path,
                'intra_distance': intra_mean,
                'threshold': self.cosine_distance_threshold,
                'message': f"High intra-cluster distance ({intra_mean:.3f})"
            })

        # Flag 3: Low inter-cluster distance
        inter_mean = cluster_metrics["inter_cluster_distances"]["mean"]
        if inter_mean < self.cosine_distance_threshold:
            flags.append({
                'type': 'low_inter_cluster_distance',
                'severity': 'medium',
                'file': file_path,
                'inter_distance': inter_mean,
                'threshold': self.cosine_distance_threshold,
                'message': f"Low inter-cluster distance ({inter_mean:.3f})"
            })

        return flags

    def _calculate_cluster_statistics(self, cluster_metrics: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate overall cluster statistics."""
        separation_scores = [m.get("separation_score", 0) for m in cluster_metrics if "separation_score" in m]
        intra_means = [m["intra_cluster_distances"]["mean"] for m in cluster_metrics]
        inter_means = [m["inter_cluster_distances"]["mean"] for m in cluster_metrics]

        quality_distribution = {}
        for metrics in cluster_metrics:
            quality = metrics.get("cluster_quality", "unknown")
            quality_distribution[quality] = quality_distribution.get(quality, 0) + 1

        return {
            "total_files_analyzed": len(cluster_metrics),
            "separation_scores": {
                "mean": np.mean(separation_scores) if separation_scores else 0,
                "std": np.std(separation_scores) if separation_scores else 0,
                "min": np.min(separation_scores) if separation_scores else 0,
                "max": np.max(separation_scores) if separation_scores else 0
            },
            "intra_cluster_distances": {
                "mean": np.mean(intra_means) if intra_means else 0,
                "std": np.std(intra_means) if intra_means else 0
            },
            "inter_cluster_distances": {
                "mean": np.mean(inter_means) if inter_means else 0,
                "std": np.std(inter_means) if inter_means else 0
            },
            "cluster_quality_distribution": quality_distribution,
            "files_with_good_clusters": quality_distribution.get("good", 0),
            "files_with_poor_clusters": quality_distribution.get("poor", 0)
        }

    def _generate_enhanced_summary(self, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate enhanced summary of all test results."""
        summary = {
            "total_files_tested": 0,
            "total_flags_raised": 0,
            "flag_breakdown": {},
            "test_completion": {},
            "overall_quality_assessment": "unknown"
        }

        # Count total flags
        total_flags = 0
        flag_types = {}

        for flag_category, flags in test_results.get("flags", {}).items():
            total_flags += len(flags)
            for flag in flags:
                flag_type = flag.get("type", "unknown")
                flag_types[flag_type] = flag_types.get(flag_type, 0) + 1

        summary["total_flags_raised"] = total_flags
        summary["flag_breakdown"] = flag_types

        # Test completion status
        tests = test_results.get("tests", {})
        for test_name, test_result in tests.items():
            if "error" in test_result:
                summary["test_completion"][test_name] = "failed"
            elif test_result.get("status") == "skipped":
                summary["test_completion"][test_name] = "skipped"
            else:
                summary["test_completion"][test_name] = "completed"

        # Overall quality assessment
        if total_flags == 0:
            summary["overall_quality_assessment"] = "excellent"
        elif total_flags <= 2:
            summary["overall_quality_assessment"] = "good"
        elif total_flags <= 5:
            summary["overall_quality_assessment"] = "fair"
        else:
            summary["overall_quality_assessment"] = "poor"

        # Add specific insights
        summary["insights"] = self._generate_insights(test_results)

        return summary

    def _generate_insights(self, test_results: Dict[str, Any]) -> List[str]:
        """Generate actionable insights from test results."""
        insights = []

        # Speaker count insights
        speaker_test = test_results.get("tests", {}).get("speaker_count_sanity", {})
        if speaker_test:
            stats = speaker_test.get("statistics", {})
            if stats.get("files_exceeding_threshold", 0) > 0:
                insights.append("Some files have excessive speaker counts - consider adjusting clustering parameters")



        # Cluster insights
        cluster_test = test_results.get("tests", {}).get("cluster_evaluation", {})
        if cluster_test and "statistics" in cluster_test:
            stats = cluster_test["statistics"]
            poor_clusters = stats.get("files_with_poor_clusters", 0)
            total_files = stats.get("total_files_analyzed", 1)

            if poor_clusters / total_files > 0.3:
                insights.append("Poor cluster quality detected - consider improving speaker embeddings or clustering algorithm")

        if not insights:
            insights.append("No significant issues detected in diarization quality")

        return insights

