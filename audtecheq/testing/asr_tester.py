"""
ASR testing and evaluation for AudioTechEquity pipeline.
"""

import re
import json
from typing import Dict, Any, List, Optional, Union
from pathlib import Path

import pandas as pd
import numpy as np
from sklearn.metrics import classification_report

from ..core.base import BaseProcessor
from ..core.exceptions import ProcessingError, ValidationError
from .metrics_calculator import MetricsCalculator


class ASRTester(BaseProcessor):
    """
    ASR testing and evaluation processor.
    
    Evaluates ASR quality using WER, CER, confidence scores,
    and keyword recognition accuracy.
    """
    
    def __init__(self, **kwargs):
        """
        Initialize the ASR tester.
        
        Parameters
        ----------
        **kwargs
            Additional arguments passed to base class
        """
        super().__init__(**kwargs)
        self.metrics_calculator = MetricsCalculator(logger=self.logger)
    
    def process(self, input_path: Union[str, Path], output_path: Union[str, Path], **kwargs) -> Dict[str, Any]:
        """
        Process ASR results for testing and evaluation.
        
        Parameters
        ----------
        input_path : str or Path
            Path to ASR results file or directory
        output_path : str or Path
            Path to output evaluation report
        **kwargs
            Additional processing parameters:
            - reference_path : str, optional
                Path to reference transcriptions for WER/CER calculation
            - keywords_path : str, optional
                Path to keyword list for keyword recognition testing
            - confidence_threshold : float, default=0.7
                Confidence threshold for quality assessment
            
        Returns
        -------
        dict
            ASR evaluation results
        """
        input_path = self.validate_input_path(input_path)
        output_path = Path(output_path)
        self.ensure_output_dir(output_path.parent)
        
        try:
            self.logger.info(f"Processing ASR evaluation for {input_path}")
            
            # Load ASR results
            asr_results = self._load_asr_results(input_path)
            
            # Perform evaluations
            evaluation_results = {
                "input_file": str(input_path),
                "total_files": len(asr_results),
                "evaluations": {}
            }
            
            # Basic quality assessment
            quality_assessment = self._assess_quality(asr_results, **kwargs)
            evaluation_results["evaluations"]["quality_assessment"] = quality_assessment
            
            # Confidence analysis
            confidence_analysis = self._analyze_confidence(asr_results, **kwargs)
            evaluation_results["evaluations"]["confidence_analysis"] = confidence_analysis
            
            # WER/CER evaluation if reference provided
            reference_path = kwargs.get('reference_path')
            if reference_path:
                wer_cer_results = self._evaluate_wer_cer(asr_results, reference_path)
                evaluation_results["evaluations"]["wer_cer"] = wer_cer_results
            
            # Keyword recognition evaluation if keywords provided
            keywords_path = kwargs.get('keywords_path')
            if keywords_path:
                keyword_results = self._evaluate_keywords(asr_results, keywords_path)
                evaluation_results["evaluations"]["keyword_recognition"] = keyword_results
            
            # Generate summary
            summary = self._generate_summary(evaluation_results)
            evaluation_results["summary"] = summary
            
            # Save results
            self._save_evaluation_results(evaluation_results, output_path)
            
            self.logger.info(f"ASR evaluation completed")
            
            return {
                "input_file": str(input_path),
                "output_file": str(output_path),
                "total_files_evaluated": len(asr_results),
                "summary": summary,
                "success": True
            }
            
        except Exception as e:
            error_msg = f"ASR evaluation failed for {input_path}: {str(e)}"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg) from e
    
    def _load_asr_results(self, input_path: Path) -> List[Dict[str, Any]]:
        """
        Load ASR results from file or directory.
        
        Parameters
        ----------
        input_path : Path
            Path to ASR results
            
        Returns
        -------
        list
            List of ASR results
        """
        asr_results = []
        
        if input_path.is_file():
            # Single file
            result = self._load_single_asr_file(input_path)
            if result:
                asr_results.append(result)
        
        elif input_path.is_dir():
            # Directory of files
            for file_path in input_path.iterdir():
                if file_path.suffix in ['.json', '.txt', '.csv']:
                    result = self._load_single_asr_file(file_path)
                    if result:
                        asr_results.append(result)
        
        return asr_results
    
    def _load_single_asr_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """
        Load single ASR result file.
        
        Parameters
        ----------
        file_path : Path
            Path to ASR file
            
        Returns
        -------
        dict or None
            ASR result data
        """
        try:
            if file_path.suffix == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                data['file_path'] = str(file_path)
                return data
            
            elif file_path.suffix == '.txt':
                with open(file_path, 'r', encoding='utf-8') as f:
                    text = f.read()
                return {
                    'text': text,
                    'file_path': str(file_path),
                    'segments': []
                }
            
            elif file_path.suffix == '.csv':
                df = pd.read_csv(file_path)
                text = " ".join(df['text'].astype(str))
                segments = df.to_dict('records')
                return {
                    'text': text,
                    'segments': segments,
                    'file_path': str(file_path)
                }
            
        except Exception as e:
            self.logger.warning(f"Failed to load ASR file {file_path}: {e}")
            return None
    
    def _assess_quality(self, asr_results: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
        """
        Assess overall quality of ASR results.
        
        Parameters
        ----------
        asr_results : list
            List of ASR results
        **kwargs
            Additional parameters
            
        Returns
        -------
        dict
            Quality assessment results
        """
        if not asr_results:
            return {"error": "No ASR results to evaluate"}
        
        quality_metrics = {
            "total_files": len(asr_results),
            "files_with_text": 0,
            "files_with_segments": 0,
            "files_with_confidence": 0,
            "average_text_length": 0,
            "average_word_count": 0,
            "empty_transcriptions": 0
        }
        
        text_lengths = []
        word_counts = []
        
        for result in asr_results:
            text = result.get('text', '')
            segments = result.get('segments', [])
            
            if text.strip():
                quality_metrics["files_with_text"] += 1
                text_lengths.append(len(text))
                word_counts.append(len(text.split()))
            else:
                quality_metrics["empty_transcriptions"] += 1
            
            if segments:
                quality_metrics["files_with_segments"] += 1
                
                # Check for confidence scores
                if any('confidence' in seg for seg in segments):
                    quality_metrics["files_with_confidence"] += 1
        
        if text_lengths:
            quality_metrics["average_text_length"] = np.mean(text_lengths)
            quality_metrics["average_word_count"] = np.mean(word_counts)
            quality_metrics["min_text_length"] = min(text_lengths)
            quality_metrics["max_text_length"] = max(text_lengths)
        
        # Calculate quality score
        quality_score = self._calculate_quality_score(quality_metrics)
        quality_metrics["overall_quality_score"] = quality_score
        
        return quality_metrics
    
    def _analyze_confidence(self, asr_results: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
        """
        Analyze confidence scores in ASR results.
        
        Parameters
        ----------
        asr_results : list
            List of ASR results
        **kwargs
            Additional parameters
            
        Returns
        -------
        dict
            Confidence analysis results
        """
        confidence_threshold = kwargs.get('confidence_threshold', 0.7)
        
        all_confidences = []
        low_confidence_segments = []
        
        for result in asr_results:
            segments = result.get('segments', [])
            file_path = result.get('file_path', 'unknown')
            
            for segment in segments:
                confidence = segment.get('confidence')
                if confidence is not None:
                    all_confidences.append(confidence)
                    
                    if confidence < confidence_threshold:
                        low_confidence_segments.append({
                            'file': file_path,
                            'text': segment.get('text', ''),
                            'confidence': confidence,
                            'start': segment.get('start'),
                            'end': segment.get('end')
                        })
        
        if not all_confidences:
            return {"error": "No confidence scores found in ASR results"}
        
        analysis = {
            "total_segments_with_confidence": len(all_confidences),
            "average_confidence": np.mean(all_confidences),
            "min_confidence": min(all_confidences),
            "max_confidence": max(all_confidences),
            "std_confidence": np.std(all_confidences),
            "confidence_threshold": confidence_threshold,
            "low_confidence_segments": len(low_confidence_segments),
            "low_confidence_rate": len(low_confidence_segments) / len(all_confidences),
            "confidence_distribution": self._calculate_confidence_distribution(all_confidences)
        }
        
        # Add examples of low confidence segments
        if low_confidence_segments:
            analysis["low_confidence_examples"] = low_confidence_segments[:10]  # First 10 examples
        
        return analysis
    
    def _evaluate_wer_cer(self, asr_results: List[Dict[str, Any]], reference_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Evaluate Word Error Rate (WER) and Character Error Rate (CER).
        
        Parameters
        ----------
        asr_results : list
            List of ASR results
        reference_path : str or Path
            Path to reference transcriptions
            
        Returns
        -------
        dict
            WER/CER evaluation results
        """
        reference_path = Path(reference_path)
        
        # Load reference transcriptions
        references = self._load_reference_transcriptions(reference_path)
        
        wer_scores = []
        cer_scores = []
        evaluations = []
        
        for result in asr_results:
            file_path = Path(result.get('file_path', ''))
            hypothesis = result.get('text', '')
            
            # Find matching reference
            reference = self._find_matching_reference(file_path, references)
            
            if reference:
                # Calculate WER and CER
                wer = self.metrics_calculator.calculate_wer(reference, hypothesis)
                cer = self.metrics_calculator.calculate_cer(reference, hypothesis)
                
                wer_scores.append(wer)
                cer_scores.append(cer)
                
                evaluations.append({
                    'file': str(file_path),
                    'wer': wer,
                    'cer': cer,
                    'reference_length': len(reference.split()),
                    'hypothesis_length': len(hypothesis.split())
                })
        
        if not wer_scores:
            return {"error": "No matching reference transcriptions found"}
        
        return {
            "total_evaluations": len(evaluations),
            "average_wer": np.mean(wer_scores),
            "average_cer": np.mean(cer_scores),
            "min_wer": min(wer_scores),
            "max_wer": max(wer_scores),
            "min_cer": min(cer_scores),
            "max_cer": max(cer_scores),
            "std_wer": np.std(wer_scores),
            "std_cer": np.std(cer_scores),
            "detailed_evaluations": evaluations
        }
    
    def _evaluate_keywords(self, asr_results: List[Dict[str, Any]], keywords_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Evaluate keyword recognition accuracy.
        
        Parameters
        ----------
        asr_results : list
            List of ASR results
        keywords_path : str or Path
            Path to keyword list
            
        Returns
        -------
        dict
            Keyword evaluation results
        """
        # Load keywords
        keywords = self._load_keywords(keywords_path)
        
        from ..keywords import KeywordExtractor
        keyword_extractor = KeywordExtractor(logger=self.logger)
        
        keyword_results = []
        all_found_keywords = []
        
        for result in asr_results:
            text = result.get('text', '')
            file_path = result.get('file_path', '')
            
            # Extract keywords
            found_keywords = keyword_extractor.extract_keywords(text, keywords)
            
            keyword_results.append({
                'file': file_path,
                'text': text,
                'found_keywords': found_keywords,
                'keyword_count': len(found_keywords)
            })
            
            all_found_keywords.extend(found_keywords)
        
        # Calculate statistics
        total_keywords_found = len(all_found_keywords)
        exact_matches = sum(1 for kw in all_found_keywords if kw.get('exact_match', False))
        
        # Calculate per-keyword statistics
        keyword_stats = {}
        for keyword in keywords:
            matches = [kw for kw in all_found_keywords if kw.get('target_keyword', '').lower() == keyword.lower()]
            keyword_stats[keyword] = {
                'total_matches': len(matches),
                'exact_matches': sum(1 for m in matches if m.get('exact_match', False)),
                'average_similarity': np.mean([m.get('similarity_score', 0) for m in matches]) if matches else 0
            }
        
        return {
            "total_files": len(keyword_results),
            "total_keywords_searched": len(keywords),
            "total_keywords_found": total_keywords_found,
            "exact_matches": exact_matches,
            "fuzzy_matches": total_keywords_found - exact_matches,
            "exact_match_rate": exact_matches / total_keywords_found if total_keywords_found > 0 else 0,
            "average_keywords_per_file": total_keywords_found / len(keyword_results) if keyword_results else 0,
            "keyword_statistics": keyword_stats,
            "detailed_results": keyword_results
        }
    
    def _calculate_quality_score(self, metrics: Dict[str, Any]) -> float:
        """Calculate overall quality score from metrics."""
        total_files = metrics.get("total_files", 1)
        
        # Factors contributing to quality
        text_coverage = metrics.get("files_with_text", 0) / total_files
        segment_coverage = metrics.get("files_with_segments", 0) / total_files
        confidence_coverage = metrics.get("files_with_confidence", 0) / total_files
        empty_rate = metrics.get("empty_transcriptions", 0) / total_files
        
        # Calculate weighted score
        quality_score = (
            text_coverage * 0.4 +
            segment_coverage * 0.3 +
            confidence_coverage * 0.2 +
            (1 - empty_rate) * 0.1
        )
        
        return min(1.0, max(0.0, quality_score))
    
    def _calculate_confidence_distribution(self, confidences: List[float]) -> Dict[str, int]:
        """Calculate confidence score distribution."""
        bins = {
            "very_low (0.0-0.3)": 0,
            "low (0.3-0.5)": 0,
            "medium (0.5-0.7)": 0,
            "high (0.7-0.9)": 0,
            "very_high (0.9-1.0)": 0
        }
        
        for conf in confidences:
            if conf < 0.3:
                bins["very_low (0.0-0.3)"] += 1
            elif conf < 0.5:
                bins["low (0.3-0.5)"] += 1
            elif conf < 0.7:
                bins["medium (0.5-0.7)"] += 1
            elif conf < 0.9:
                bins["high (0.7-0.9)"] += 1
            else:
                bins["very_high (0.9-1.0)"] += 1
        
        return bins
    
    def _load_reference_transcriptions(self, reference_path: Path) -> Dict[str, str]:
        """Load reference transcriptions from file or directory."""
        references = {}
        
        if reference_path.is_file():
            if reference_path.suffix == '.json':
                with open(reference_path, 'r', encoding='utf-8') as f:
                    references = json.load(f)
            elif reference_path.suffix == '.txt':
                with open(reference_path, 'r', encoding='utf-8') as f:
                    # Assume single reference
                    references[reference_path.stem] = f.read().strip()
        
        elif reference_path.is_dir():
            for file_path in reference_path.iterdir():
                if file_path.suffix == '.txt':
                    with open(file_path, 'r', encoding='utf-8') as f:
                        references[file_path.stem] = f.read().strip()
        
        return references
    
    def _find_matching_reference(self, file_path: Path, references: Dict[str, str]) -> Optional[str]:
        """Find matching reference transcription for a file."""
        # Try exact stem match first
        stem = file_path.stem
        if stem in references:
            return references[stem]
        
        # Try without common suffixes
        for suffix in ['_transcription', '_asr', '_whisper']:
            if stem.endswith(suffix):
                clean_stem = stem[:-len(suffix)]
                if clean_stem in references:
                    return references[clean_stem]
        
        return None
    
    def _load_keywords(self, keywords_path: Path) -> List[str]:
        """Load keywords from file."""
        keywords = []
        
        if keywords_path.suffix == '.txt':
            with open(keywords_path, 'r', encoding='utf-8') as f:
                keywords = [line.strip() for line in f if line.strip()]
        elif keywords_path.suffix == '.json':
            with open(keywords_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if isinstance(data, list):
                    keywords = data
                elif isinstance(data, dict) and 'keywords' in data:
                    keywords = data['keywords']
        
        return keywords
    
    def _generate_summary(self, evaluation_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate evaluation summary."""
        summary = {
            "total_files_evaluated": evaluation_results.get("total_files", 0),
            "evaluations_performed": list(evaluation_results.get("evaluations", {}).keys())
        }
        
        # Add key metrics from each evaluation
        evaluations = evaluation_results.get("evaluations", {})
        
        if "quality_assessment" in evaluations:
            qa = evaluations["quality_assessment"]
            summary["overall_quality_score"] = qa.get("overall_quality_score", 0)
        
        if "confidence_analysis" in evaluations:
            ca = evaluations["confidence_analysis"]
            summary["average_confidence"] = ca.get("average_confidence", 0)
            summary["low_confidence_rate"] = ca.get("low_confidence_rate", 0)
        
        if "wer_cer" in evaluations:
            wc = evaluations["wer_cer"]
            summary["average_wer"] = wc.get("average_wer", 0)
            summary["average_cer"] = wc.get("average_cer", 0)
        
        if "keyword_recognition" in evaluations:
            kr = evaluations["keyword_recognition"]
            summary["keyword_exact_match_rate"] = kr.get("exact_match_rate", 0)
            summary["total_keywords_found"] = kr.get("total_keywords_found", 0)
        
        return summary
    
    def _save_evaluation_results(self, results: Dict[str, Any], output_path: Path) -> None:
        """Save evaluation results to file."""
        if output_path.suffix != '.json':
            output_path = output_path.with_suffix('.json')
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
