"""
Diarization testing and evaluation for AudioTechEquity pipeline.
"""

import json
import pandas as pd
from typing import Dict, Any, List, Optional, Union
from pathlib import Path

from ..core.base import BaseProcessor
from ..core.exceptions import ProcessingError, ValidationError
from .metrics_calculator import MetricsCalculator


class DiarizationTester(BaseProcessor):
    """
    Diarization testing and evaluation processor.
    
    Evaluates diarization quality including speaker count accuracy,
    segment duration analysis, and speaker balance assessment.
    """
    
    def __init__(self, **kwargs):
        """
        Initialize the diarization tester.
        
        Parameters
        ----------
        **kwargs
            Additional arguments passed to base class
        """
        super().__init__(**kwargs)
        self.metrics_calculator = MetricsCalculator(logger=self.logger)
    
    def process(self, input_path: Union[str, Path], output_path: Union[str, Path], **kwargs) -> Dict[str, Any]:
        """
        Process diarization results for testing and evaluation.
        
        Parameters
        ----------
        input_path : str or Path
            Path to diarization results directory or file
        output_path : str or Path
            Path to output evaluation report
        **kwargs
            Additional processing parameters:
            - expected_speakers : int, optional
                Expected number of speakers for validation
            - min_segment_duration : float, default=0.5
                Minimum expected segment duration
            - max_segment_duration : float, default=30.0
                Maximum expected segment duration
            
        Returns
        -------
        dict
            Diarization evaluation results
        """
        input_path = self.validate_input_path(input_path)
        output_path = Path(output_path)
        self.ensure_output_dir(output_path.parent)
        
        try:
            self.logger.info(f"Processing diarization evaluation for {input_path}")
            
            # Load diarization results
            diarization_data = self._load_diarization_data(input_path)
            
            # Perform evaluations
            evaluation_results = {
                "input_path": str(input_path),
                "total_files": len(diarization_data),
                "evaluations": {}
            }
            
            # Speaker count analysis
            speaker_analysis = self._analyze_speaker_counts(diarization_data, **kwargs)
            evaluation_results["evaluations"]["speaker_analysis"] = speaker_analysis
            
            # Segment duration analysis
            duration_analysis = self._analyze_segment_durations(diarization_data, **kwargs)
            evaluation_results["evaluations"]["duration_analysis"] = duration_analysis
            
            # Speaker balance analysis
            balance_analysis = self._analyze_speaker_balance(diarization_data)
            evaluation_results["evaluations"]["balance_analysis"] = balance_analysis
            
            # Quality assessment
            quality_assessment = self._assess_diarization_quality(diarization_data, **kwargs)
            evaluation_results["evaluations"]["quality_assessment"] = quality_assessment
            
            # Generate summary
            summary = self._generate_summary(evaluation_results)
            evaluation_results["summary"] = summary
            
            # Save results
            self._save_evaluation_results(evaluation_results, output_path)
            
            self.logger.info(f"Diarization evaluation completed")
            
            return {
                "input_path": str(input_path),
                "output_file": str(output_path),
                "total_files_evaluated": len(diarization_data),
                "summary": summary,
                "success": True
            }
            
        except Exception as e:
            error_msg = f"Diarization evaluation failed for {input_path}: {str(e)}"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg) from e
    
    def _load_diarization_data(self, input_path: Path) -> List[Dict[str, Any]]:
        """
        Load diarization data from file or directory.
        
        Parameters
        ----------
        input_path : Path
            Path to diarization results
            
        Returns
        -------
        list
            List of diarization data
        """
        diarization_data = []
        
        if input_path.is_file():
            # Single file
            data = self._load_single_diarization_file(input_path)
            if data:
                diarization_data.append(data)
        
        elif input_path.is_dir():
            # Directory of files
            for file_path in input_path.iterdir():
                if file_path.suffix in ['.csv', '.json', '.rttm']:
                    data = self._load_single_diarization_file(file_path)
                    if data:
                        diarization_data.append(data)
        
        return diarization_data
    
    def _load_single_diarization_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """
        Load single diarization file.
        
        Parameters
        ----------
        file_path : Path
            Path to diarization file
            
        Returns
        -------
        dict or None
            Diarization data
        """
        try:
            if file_path.suffix == '.csv':
                df = pd.read_csv(file_path)
                segments = df.to_dict('records')
                return {
                    'file_path': str(file_path),
                    'segments': segments,
                    'format': 'csv'
                }
            
            elif file_path.suffix == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                return {
                    'file_path': str(file_path),
                    'segments': data.get('segments', []),
                    'format': 'json'
                }
            
            elif file_path.suffix == '.rttm':
                segments = self._parse_rttm_file(file_path)
                return {
                    'file_path': str(file_path),
                    'segments': segments,
                    'format': 'rttm'
                }
            
        except Exception as e:
            self.logger.warning(f"Failed to load diarization file {file_path}: {e}")
            return None
    
    def _parse_rttm_file(self, rttm_file: Path) -> List[Dict[str, Any]]:
        """Parse RTTM file format."""
        segments = []
        
        with open(rttm_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    parts = line.split()
                    if len(parts) >= 8 and parts[0] == 'SPEAKER':
                        start_time = float(parts[3])
                        duration = float(parts[4])
                        end_time = start_time + duration
                        speaker_id = parts[7]
                        
                        segments.append({
                            'start_time': start_time,
                            'end_time': end_time,
                            'duration': duration,
                            'speaker': speaker_id
                        })
        
        return segments
    
    def _analyze_speaker_counts(self, diarization_data: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
        """
        Analyze speaker count accuracy.
        
        Parameters
        ----------
        diarization_data : list
            List of diarization data
        **kwargs
            Additional parameters
            
        Returns
        -------
        dict
            Speaker count analysis
        """
        expected_speakers = kwargs.get('expected_speakers')
        
        speaker_counts = []
        files_analysis = []
        
        for data in diarization_data:
            segments = data['segments']
            speakers = set(seg['speaker'] for seg in segments)
            speaker_count = len(speakers)
            speaker_counts.append(speaker_count)
            
            file_analysis = {
                'file': data['file_path'],
                'detected_speakers': speaker_count,
                'speakers': list(speakers)
            }
            
            if expected_speakers is not None:
                file_analysis['expected_speakers'] = expected_speakers
                file_analysis['count_accurate'] = speaker_count == expected_speakers
                file_analysis['count_difference'] = speaker_count - expected_speakers
            
            files_analysis.append(file_analysis)
        
        analysis = {
            'total_files': len(diarization_data),
            'speaker_counts': speaker_counts,
            'average_speakers': sum(speaker_counts) / len(speaker_counts) if speaker_counts else 0,
            'min_speakers': min(speaker_counts) if speaker_counts else 0,
            'max_speakers': max(speaker_counts) if speaker_counts else 0,
            'files_analysis': files_analysis
        }
        
        if expected_speakers is not None:
            accurate_files = sum(1 for fa in files_analysis if fa.get('count_accurate', False))
            analysis['expected_speakers'] = expected_speakers
            analysis['count_accuracy'] = accurate_files / len(files_analysis) if files_analysis else 0
            analysis['accurate_files'] = accurate_files
        
        return analysis
    
    def _analyze_segment_durations(self, diarization_data: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
        """
        Analyze segment duration characteristics.
        
        Parameters
        ----------
        diarization_data : list
            List of diarization data
        **kwargs
            Additional parameters
            
        Returns
        -------
        dict
            Duration analysis
        """
        min_duration = kwargs.get('min_segment_duration', 0.5)
        max_duration = kwargs.get('max_segment_duration', 30.0)
        
        all_durations = []
        short_segments = []
        long_segments = []
        
        for data in diarization_data:
            segments = data['segments']
            
            for segment in segments:
                duration = segment.get('duration', 0)
                all_durations.append(duration)
                
                if duration < min_duration:
                    short_segments.append({
                        'file': data['file_path'],
                        'duration': duration,
                        'speaker': segment.get('speaker'),
                        'start_time': segment.get('start_time')
                    })
                
                if duration > max_duration:
                    long_segments.append({
                        'file': data['file_path'],
                        'duration': duration,
                        'speaker': segment.get('speaker'),
                        'start_time': segment.get('start_time')
                    })
        
        if not all_durations:
            return {"error": "No segments found for duration analysis"}
        
        import numpy as np
        
        return {
            'total_segments': len(all_durations),
            'average_duration': np.mean(all_durations),
            'median_duration': np.median(all_durations),
            'min_duration': min(all_durations),
            'max_duration': max(all_durations),
            'std_duration': np.std(all_durations),
            'short_segments': len(short_segments),
            'long_segments': len(long_segments),
            'short_segment_rate': len(short_segments) / len(all_durations),
            'long_segment_rate': len(long_segments) / len(all_durations),
            'duration_thresholds': {
                'min_threshold': min_duration,
                'max_threshold': max_duration
            },
            'short_segment_examples': short_segments[:10],  # First 10 examples
            'long_segment_examples': long_segments[:10]     # First 10 examples
        }
    
    def _analyze_speaker_balance(self, diarization_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze speaker balance and distribution.
        
        Parameters
        ----------
        diarization_data : list
            List of diarization data
            
        Returns
        -------
        dict
            Speaker balance analysis
        """
        balance_analysis = []
        
        for data in diarization_data:
            segments = data['segments']
            
            # Calculate speaker durations
            speaker_durations = {}
            total_duration = 0
            
            for segment in segments:
                speaker = segment.get('speaker')
                duration = segment.get('duration', 0)
                
                if speaker not in speaker_durations:
                    speaker_durations[speaker] = 0
                speaker_durations[speaker] += duration
                total_duration += duration
            
            # Calculate balance metrics
            if speaker_durations and total_duration > 0:
                durations = list(speaker_durations.values())
                
                # Calculate balance ratio (min/max)
                balance_ratio = min(durations) / max(durations) if max(durations) > 0 else 0
                
                # Calculate speaker percentages
                speaker_percentages = {
                    speaker: (duration / total_duration) * 100
                    for speaker, duration in speaker_durations.items()
                }
                
                balance_analysis.append({
                    'file': data['file_path'],
                    'total_duration': total_duration,
                    'speaker_durations': speaker_durations,
                    'speaker_percentages': speaker_percentages,
                    'balance_ratio': balance_ratio,
                    'num_speakers': len(speaker_durations),
                    'dominant_speaker': max(speaker_durations.items(), key=lambda x: x[1])[0]
                })
        
        # Calculate overall statistics
        if balance_analysis:
            balance_ratios = [ba['balance_ratio'] for ba in balance_analysis]
            import numpy as np
            
            return {
                'total_files': len(balance_analysis),
                'average_balance_ratio': np.mean(balance_ratios),
                'min_balance_ratio': min(balance_ratios),
                'max_balance_ratio': max(balance_ratios),
                'files_analysis': balance_analysis,
                'balance_interpretation': self._interpret_balance_ratios(balance_ratios)
            }
        
        return {"error": "No valid segments found for balance analysis"}
    
    def _assess_diarization_quality(self, diarization_data: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
        """
        Assess overall diarization quality.
        
        Parameters
        ----------
        diarization_data : list
            List of diarization data
        **kwargs
            Additional parameters
            
        Returns
        -------
        dict
            Quality assessment
        """
        quality_scores = []
        quality_issues = []
        
        for data in diarization_data:
            segments = data['segments']
            file_path = data['file_path']
            
            # Calculate quality factors
            quality_factors = []
            issues = []
            
            # Factor 1: Reasonable number of speakers
            speakers = set(seg['speaker'] for seg in segments)
            num_speakers = len(speakers)
            
            if 1 <= num_speakers <= 6:
                quality_factors.append(1.0)
            elif num_speakers > 6:
                quality_factors.append(0.7)
                issues.append(f"High number of speakers ({num_speakers})")
            else:
                quality_factors.append(0.0)
                issues.append("No speakers detected")
            
            # Factor 2: Segment duration distribution
            durations = [seg.get('duration', 0) for seg in segments]
            if durations:
                avg_duration = sum(durations) / len(durations)
                short_segments = sum(1 for d in durations if d < 0.5)
                short_ratio = short_segments / len(durations)
                
                if short_ratio < 0.3:
                    quality_factors.append(1.0)
                elif short_ratio < 0.5:
                    quality_factors.append(0.7)
                    issues.append(f"Some short segments ({short_ratio:.1%})")
                else:
                    quality_factors.append(0.4)
                    issues.append(f"Many short segments ({short_ratio:.1%})")
            
            # Factor 3: Speaker balance
            speaker_durations = {}
            for segment in segments:
                speaker = segment.get('speaker')
                duration = segment.get('duration', 0)
                speaker_durations[speaker] = speaker_durations.get(speaker, 0) + duration
            
            if len(speaker_durations) > 1:
                durations_list = list(speaker_durations.values())
                balance_ratio = min(durations_list) / max(durations_list)
                
                if balance_ratio > 0.3:
                    quality_factors.append(1.0)
                elif balance_ratio > 0.1:
                    quality_factors.append(0.8)
                    issues.append("Moderate speaker imbalance")
                else:
                    quality_factors.append(0.5)
                    issues.append("High speaker imbalance")
            else:
                quality_factors.append(0.8)  # Single speaker is okay
            
            # Calculate overall quality score
            quality_score = sum(quality_factors) / len(quality_factors) if quality_factors else 0
            quality_scores.append(quality_score)
            
            if issues:
                quality_issues.append({
                    'file': file_path,
                    'quality_score': quality_score,
                    'issues': issues
                })
        
        import numpy as np
        
        return {
            'total_files': len(diarization_data),
            'average_quality_score': np.mean(quality_scores) if quality_scores else 0,
            'min_quality_score': min(quality_scores) if quality_scores else 0,
            'max_quality_score': max(quality_scores) if quality_scores else 0,
            'files_with_issues': len(quality_issues),
            'quality_issues': quality_issues,
            'quality_distribution': self._calculate_quality_distribution(quality_scores)
        }
    
    def _interpret_balance_ratios(self, balance_ratios: List[float]) -> Dict[str, Any]:
        """Interpret balance ratio statistics."""
        import numpy as np
        
        avg_ratio = np.mean(balance_ratios)
        
        if avg_ratio > 0.7:
            interpretation = "Excellent speaker balance"
        elif avg_ratio > 0.5:
            interpretation = "Good speaker balance"
        elif avg_ratio > 0.3:
            interpretation = "Moderate speaker balance"
        else:
            interpretation = "Poor speaker balance"
        
        return {
            "interpretation": interpretation,
            "average_ratio": avg_ratio,
            "balance_quality": "high" if avg_ratio > 0.5 else "medium" if avg_ratio > 0.3 else "low"
        }
    
    def _calculate_quality_distribution(self, quality_scores: List[float]) -> Dict[str, int]:
        """Calculate quality score distribution."""
        distribution = {
            "excellent (0.9-1.0)": 0,
            "good (0.7-0.9)": 0,
            "fair (0.5-0.7)": 0,
            "poor (0.0-0.5)": 0
        }
        
        for score in quality_scores:
            if score >= 0.9:
                distribution["excellent (0.9-1.0)"] += 1
            elif score >= 0.7:
                distribution["good (0.7-0.9)"] += 1
            elif score >= 0.5:
                distribution["fair (0.5-0.7)"] += 1
            else:
                distribution["poor (0.0-0.5)"] += 1
        
        return distribution
    
    def _generate_summary(self, evaluation_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate evaluation summary."""
        summary = {
            "total_files_evaluated": evaluation_results.get("total_files", 0),
            "evaluations_performed": list(evaluation_results.get("evaluations", {}).keys())
        }
        
        # Add key metrics from each evaluation
        evaluations = evaluation_results.get("evaluations", {})
        
        if "speaker_analysis" in evaluations:
            sa = evaluations["speaker_analysis"]
            summary["average_speakers_detected"] = sa.get("average_speakers", 0)
            if "count_accuracy" in sa:
                summary["speaker_count_accuracy"] = sa["count_accuracy"]
        
        if "quality_assessment" in evaluations:
            qa = evaluations["quality_assessment"]
            summary["average_quality_score"] = qa.get("average_quality_score", 0)
            summary["files_with_issues"] = qa.get("files_with_issues", 0)
        
        if "balance_analysis" in evaluations:
            ba = evaluations["balance_analysis"]
            if "average_balance_ratio" in ba:
                summary["average_speaker_balance"] = ba["average_balance_ratio"]
        
        return summary
    
    def _save_evaluation_results(self, results: Dict[str, Any], output_path: Path) -> None:
        """Save evaluation results to file."""
        if output_path.suffix != '.json':
            output_path = output_path.with_suffix('.json')
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
