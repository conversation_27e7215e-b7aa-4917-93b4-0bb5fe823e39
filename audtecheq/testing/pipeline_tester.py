"""
End-to-end pipeline testing for AudioTechEquity.
"""

import json
import time
from typing import Dict, Any, List, Optional, Union
from pathlib import Path

from ..core.base import BaseProcessor
from ..core.config import PipelineConfig
from ..core.exceptions import ProcessingError
from .asr_tester import ASRTester
from .diarization_tester import DiarizationTester


class PipelineTester(BaseProcessor):
    """
    End-to-end pipeline testing and evaluation processor.
    
    Tests the complete AudioTechEquity pipeline and generates
    comprehensive evaluation reports.
    """
    
    def __init__(self, **kwargs):
        """
        Initialize the pipeline tester.
        
        Parameters
        ----------
        **kwargs
            Additional arguments passed to base class
        """
        super().__init__(**kwargs)
        self.asr_tester = ASRTester(logger=self.logger)
        self.diarization_tester = DiarizationTester(logger=self.logger)
    
    def process(self, input_path: Union[str, Path], output_path: Union[str, Path], **kwargs) -> Dict[str, Any]:
        """
        Process pipeline results for comprehensive testing and evaluation.
        
        Parameters
        ----------
        input_path : str or Path
            Path to pipeline results directory
        output_path : str or Path
            Path to output evaluation report
        **kwargs
            Additional processing parameters:
            - config_path : str, optional
                Path to pipeline configuration file
            - expected_speakers : int, optional
                Expected number of speakers
            - reference_transcriptions : str, optional
                Path to reference transcriptions
            - keywords_list : str, optional
                Path to keywords list
            
        Returns
        -------
        dict
            Pipeline evaluation results
        """
        input_path = self.validate_input_path(input_path)
        output_path = Path(output_path)
        self.ensure_output_dir(output_path.parent)
        
        try:
            self.logger.info(f"Processing pipeline evaluation for {input_path}")
            start_time = time.time()
            
            # Initialize evaluation results
            evaluation_results = {
                "input_path": str(input_path),
                "evaluation_timestamp": time.time(),
                "pipeline_components": {},
                "overall_assessment": {}
            }
            
            # Test individual components
            component_results = self._test_pipeline_components(input_path, **kwargs)
            evaluation_results["pipeline_components"] = component_results
            
            # Generate overall assessment
            overall_assessment = self._generate_overall_assessment(component_results)
            evaluation_results["overall_assessment"] = overall_assessment
            
            # Calculate processing time
            processing_time = time.time() - start_time
            evaluation_results["processing_time"] = processing_time
            
            # Save results
            self._save_evaluation_results(evaluation_results, output_path)
            
            self.logger.info(f"Pipeline evaluation completed in {processing_time:.2f}s")
            
            return {
                "input_path": str(input_path),
                "output_file": str(output_path),
                "overall_score": overall_assessment.get("overall_score", 0),
                "components_tested": len(component_results),
                "processing_time": processing_time,
                "success": True
            }
            
        except Exception as e:
            error_msg = f"Pipeline evaluation failed for {input_path}: {str(e)}"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg) from e
    
    def _test_pipeline_components(self, pipeline_dir: Path, **kwargs) -> Dict[str, Any]:
        """
        Test individual pipeline components.
        
        Parameters
        ----------
        pipeline_dir : Path
            Pipeline results directory
        **kwargs
            Additional parameters
            
        Returns
        -------
        dict
            Component test results
        """
        component_results = {}
        
        # Test preprocessing
        preprocessing_dir = pipeline_dir / "01_preprocessing"
        if preprocessing_dir.exists():
            preprocessing_results = self._test_preprocessing(preprocessing_dir)
            component_results["preprocessing"] = preprocessing_results
        
        # Test VAD
        vad_dir = pipeline_dir / "02_vad"
        if vad_dir.exists():
            vad_results = self._test_vad(vad_dir)
            component_results["vad"] = vad_results
        
        # Test diarization
        diarization_dir = pipeline_dir / "03_diarization"
        if diarization_dir.exists():
            diarization_results = self.diarization_tester.process(
                diarization_dir,
                pipeline_dir / "temp_diarization_test.json",
                expected_speakers=kwargs.get("expected_speakers")
            )
            component_results["diarization"] = diarization_results["summary"]
        
        # Test segmentation
        segmentation_dir = pipeline_dir / "04_segmentation"
        if segmentation_dir.exists():
            segmentation_results = self._test_segmentation(segmentation_dir)
            component_results["segmentation"] = segmentation_results
        
        # Test ASR
        asr_dir = pipeline_dir / "05_asr"
        if asr_dir.exists():
            asr_results = self.asr_tester.process(
                asr_dir,
                pipeline_dir / "temp_asr_test.json",
                reference_path=kwargs.get("reference_transcriptions"),
                keywords_path=kwargs.get("keywords_list")
            )
            component_results["asr"] = asr_results["summary"]
        
        return component_results
    
    def _test_preprocessing(self, preprocessing_dir: Path) -> Dict[str, Any]:
        """Test preprocessing component."""
        results = {
            "component": "preprocessing",
            "status": "tested",
            "files_found": 0,
            "quality_score": 0.8  # Placeholder
        }
        
        # Count processed files
        audio_files = list(preprocessing_dir.glob("*.wav"))
        results["files_found"] = len(audio_files)
        
        if audio_files:
            # Basic quality checks
            sample_file = audio_files[0]
            try:
                import librosa
                audio, sr = librosa.load(str(sample_file))
                
                # Check sample rate
                if sr == 16000:
                    results["quality_score"] += 0.1
                
                # Check audio length
                if len(audio) > 0:
                    results["quality_score"] += 0.1
                
                results["sample_rate"] = sr
                results["sample_duration"] = len(audio) / sr
                
            except Exception as e:
                results["error"] = str(e)
                results["quality_score"] = 0.5
        
        return results
    
    def _test_vad(self, vad_dir: Path) -> Dict[str, Any]:
        """Test VAD component."""
        results = {
            "component": "vad",
            "status": "tested",
            "files_found": 0,
            "quality_score": 0.7  # Placeholder
        }
        
        # Count VAD result files
        vad_files = list(vad_dir.glob("*.json"))
        results["files_found"] = len(vad_files)
        
        if vad_files:
            # Analyze VAD results
            sample_file = vad_files[0]
            try:
                with open(sample_file, 'r') as f:
                    vad_data = json.load(f)
                
                speech_segments = vad_data.get("speech_segments", [])
                results["speech_segments_found"] = len(speech_segments)
                
                if speech_segments:
                    total_speech_time = sum(seg["end"] - seg["start"] for seg in speech_segments)
                    results["total_speech_time"] = total_speech_time
                    results["quality_score"] = 0.9 if total_speech_time > 0 else 0.3
                
            except Exception as e:
                results["error"] = str(e)
                results["quality_score"] = 0.3
        
        return results
    
    def _test_segmentation(self, segmentation_dir: Path) -> Dict[str, Any]:
        """Test segmentation component."""
        results = {
            "component": "segmentation",
            "status": "tested",
            "files_found": 0,
            "quality_score": 0.7  # Placeholder
        }
        
        # Count segmented files
        segment_files = list(segmentation_dir.glob("*.wav"))
        results["files_found"] = len(segment_files)
        
        if segment_files:
            # Analyze segment durations
            durations = []
            for segment_file in segment_files[:10]:  # Sample first 10
                try:
                    import librosa
                    audio, sr = librosa.load(str(segment_file))
                    duration = len(audio) / sr
                    durations.append(duration)
                except Exception:
                    continue
            
            if durations:
                import numpy as np
                results["average_segment_duration"] = np.mean(durations)
                results["min_segment_duration"] = min(durations)
                results["max_segment_duration"] = max(durations)
                
                # Quality based on reasonable segment lengths
                avg_duration = np.mean(durations)
                if 1.0 <= avg_duration <= 20.0:
                    results["quality_score"] = 0.9
                else:
                    results["quality_score"] = 0.6
        
        return results
    
    def _generate_overall_assessment(self, component_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate overall pipeline assessment.
        
        Parameters
        ----------
        component_results : dict
            Results from individual component tests
            
        Returns
        -------
        dict
            Overall assessment
        """
        if not component_results:
            return {
                "overall_score": 0.0,
                "status": "failed",
                "message": "No components tested"
            }
        
        # Calculate weighted overall score
        component_weights = {
            "preprocessing": 0.15,
            "vad": 0.15,
            "diarization": 0.25,
            "segmentation": 0.15,
            "asr": 0.30
        }
        
        total_score = 0.0
        total_weight = 0.0
        
        for component, weight in component_weights.items():
            if component in component_results:
                component_score = component_results[component].get("quality_score", 0.0)
                total_score += component_score * weight
                total_weight += weight
        
        overall_score = total_score / total_weight if total_weight > 0 else 0.0
        
        # Determine status
        if overall_score >= 0.8:
            status = "excellent"
        elif overall_score >= 0.6:
            status = "good"
        elif overall_score >= 0.4:
            status = "fair"
        else:
            status = "poor"
        
        # Generate recommendations
        recommendations = self._generate_recommendations(component_results, overall_score)
        
        return {
            "overall_score": overall_score,
            "status": status,
            "components_tested": len(component_results),
            "component_scores": {comp: results.get("quality_score", 0) 
                               for comp, results in component_results.items()},
            "recommendations": recommendations,
            "summary": f"Pipeline achieved {status} performance with overall score of {overall_score:.2f}"
        }
    
    def _generate_recommendations(self, component_results: Dict[str, Any], overall_score: float) -> List[str]:
        """Generate improvement recommendations."""
        recommendations = []
        
        # Component-specific recommendations
        for component, results in component_results.items():
            quality_score = results.get("quality_score", 0)
            
            if quality_score < 0.6:
                if component == "diarization":
                    recommendations.append(f"Consider adjusting diarization parameters or using oracle speaker count")
                elif component == "asr":
                    recommendations.append(f"ASR quality is low - consider using a larger model or improving audio quality")
                elif component == "preprocessing":
                    recommendations.append(f"Audio preprocessing may need adjustment - check noise reduction settings")
        
        # Overall recommendations
        if overall_score < 0.5:
            recommendations.append("Overall pipeline performance is low - consider reviewing all component configurations")
        elif overall_score < 0.7:
            recommendations.append("Pipeline performance is moderate - focus on improving lowest-scoring components")
        
        if not recommendations:
            recommendations.append("Pipeline performance is good - consider fine-tuning for specific use cases")
        
        return recommendations
    
    def _save_evaluation_results(self, results: Dict[str, Any], output_path: Path) -> None:
        """Save evaluation results to file."""
        if output_path.suffix != '.json':
            output_path = output_path.with_suffix('.json')
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
