# AudioTechEquity Default Configuration
# This file contains default settings for the AudioTechEquity pipeline

# Global settings
device: "auto"  # auto, cpu, cuda
num_workers: 1
batch_size: 1
output_format: "wav"
log_level: "INFO"

# Preprocessing configuration
preprocessing:
  target_sample_rate: 16000
  target_channels: 1
  target_dbfs: -20.0
  noise_reduction_prop: 0.8
  noise_reduction_stationary: true

# Voice Activity Detection configuration
vad:
  model_name: "silero_vad"
  threshold: 0.5
  min_speech_duration_ms: 250
  min_silence_duration_ms: 100
  window_size_samples: 1536
  speech_pad_ms: 30

# Speaker diarization configuration
diarization:
  model_name: "nvidia/speakerverification_en_titanet_large"
  clustering_backend: "spectral"
  oracle_num_speakers: null
  max_num_speakers: 8
  enhanced_count_thres: 0.8
  sparse_search_volume: 30

# Audio segmentation configuration
segmentation:
  padding_sec: 0.2
  apply_crossfade: false
  crossfade_sec: 0.1
  noise_amplitude: 0.005
  max_segment_length: 15.0
  min_segment_length: 0.75

# Automatic Speech Recognition configuration
asr:
  model_name: "turbo"
  language: "en"
  fp16: true
  temperature: 0.0
  best_of: 5
  beam_size: 5

# Keyword recognition and testing configuration
keywords:
  gfta_words:
    - "House"
    - "Door"
    - "Pig"
    - "Cup"
    - "Boy"
    - "Apple"
    - "Go"
    - "Duck"
    - "Quack"
    - "Table"
    - "Monkey"
    - "Hammer"
    - "Fish"
    - "Watch"
    - "Spider"
    - "Web"
    - "Drum"
    - "Plate"
    - "Knife"
    - "Shoe"
    - "Slide"
    - "Swing"
    - "Guitar"
    - "Lion"
    - "Chair"
    - "Soap"
    - "Glasses"
    - "Tiger"
    - "Puzzle"
    - "Finger"
    - "Ring"
    - "Thumb"
    - "Elephant"
    - "Vacuum"
    - "Shovel"
    - "Teacher"
    - "Zebra"
    - "Giraffe"
    - "Vegetable"
    - "Brushing"
    - "Blue"
    - "Yellow"
    - "Brother"
    - "Frog"
    - "Green"
    - "That"
    - "Leaf"
    - "Cookie"
    - "Cheese"
    - "Pajamas"
    - "Teeth"
    - "Princess"
    - "Crown"
  
  word_repetition_task:
    - "cash"
    - "boat"
    - "duck"
    - "shoot"
    - "big"
    - "bed"
    - "sheep"
    - "dog"
    - "cage"
    - "cat"
    - "chop"
  
  fuzzy_match_threshold: 0.8
  edit_distance_threshold: 2
