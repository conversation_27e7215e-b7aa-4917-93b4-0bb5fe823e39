"""
Confidence scoring for ASR results.
"""

import logging
import numpy as np
from typing import Dict, Any, List, Optional


class ConfidenceScorer:
    """
    Confidence scoring for ASR transcription results.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        Initialize confidence scorer.
        
        Parameters
        ----------
        logger : logging.Logger, optional
            Logger instance
        """
        self.logger = logger or logging.getLogger(__name__)
    
    def add_confidence_scores(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add confidence scores to ASR result.
        
        Parameters
        ----------
        result : dict
            ASR result from Whisper
            
        Returns
        -------
        dict
            ASR result with confidence scores
        """
        if 'segments' not in result:
            # No segments, add overall confidence if possible
            result['confidence'] = self._estimate_overall_confidence(result)
            return result
        
        # Add confidence to each segment
        for segment in result['segments']:
            if 'confidence' not in segment:
                segment['confidence'] = self._estimate_segment_confidence(segment)
        
        # Calculate overall confidence
        segment_confidences = [seg.get('confidence', 0.5) for seg in result['segments']]
        result['confidence'] = np.mean(segment_confidences) if segment_confidences else 0.5
        
        return result
    
    def _estimate_segment_confidence(self, segment: Dict[str, Any]) -> float:
        """
        Estimate confidence for a segment.
        
        Parameters
        ----------
        segment : dict
            Segment data
            
        Returns
        -------
        float
            Confidence score (0-1)
        """
        # If avg_logprob is available, use it
        if 'avg_logprob' in segment:
            # Convert log probability to confidence
            confidence = np.exp(segment['avg_logprob'])
            return min(confidence, 1.0)
        
        # Fallback: estimate based on text characteristics
        text = segment.get('text', '')
        
        # Basic heuristics for confidence estimation
        confidence = 0.5  # Base confidence
        
        # Longer segments tend to be more reliable
        word_count = len(text.split())
        if word_count > 5:
            confidence += 0.1
        elif word_count < 2:
            confidence -= 0.2
        
        # Check for common transcription artifacts
        if any(char in text for char in ['[', ']', '(', ')']):
            confidence -= 0.1
        
        # Check for repeated words (potential artifacts)
        words = text.lower().split()
        if len(words) != len(set(words)) and len(words) > 1:
            confidence -= 0.1
        
        return max(0.0, min(1.0, confidence))
    
    def _estimate_overall_confidence(self, result: Dict[str, Any]) -> float:
        """
        Estimate overall confidence for transcription.
        
        Parameters
        ----------
        result : dict
            ASR result
            
        Returns
        -------
        float
            Overall confidence score
        """
        text = result.get('text', '')
        
        if not text.strip():
            return 0.0
        
        # Basic confidence estimation
        confidence = 0.6  # Base confidence
        
        # Text length factor
        word_count = len(text.split())
        if word_count > 10:
            confidence += 0.1
        elif word_count < 3:
            confidence -= 0.2
        
        return max(0.0, min(1.0, confidence))
