"""
Main ASR processor for AudioTechEquity pipeline.
"""

import os
import json
import time
from typing import Dict, Any, Union, Optional, List
from pathlib import Path

import torch
import whisper
from docx import Document

from ..core.base import BatchProcessor
from ..core.config import ASRConfig
from ..core.exceptions import ProcessingError, ModelLoadError
from .whisper_asr import WhisperASR
from .confidence_scorer import ConfidenceScorer


class ASRProcessor(BatchProcessor):
    """
    Automatic Speech Recognition processor.
    
    Uses OpenAI Whisper for speech recognition with confidence scoring
    and multiple output formats.
    """
    
    def __init__(self, config: Optional[ASRConfig] = None, **kwargs):
        """
        Initialize the ASR processor.
        
        Parameters
        ----------
        config : ASRConfig, optional
            ASR configuration
        **kwargs
            Additional arguments passed to base class
        """
        super().__init__(config=config.__dict__ if config else None, **kwargs)
        
        self.asr_config = config or ASRConfig()
        self.device = self._get_device()
        
        # Initialize Whisper model
        self.whisper_asr = WhisperASR(
            model_name=self.asr_config.model_name,
            device=self.device,
            logger=self.logger
        )
        
        # Initialize confidence scorer
        self.confidence_scorer = ConfidenceScorer(logger=self.logger)
    
    def _get_device(self) -> str:
        """Get appropriate device for processing."""
        if torch.cuda.is_available():
            return "cuda"
        return "cpu"
    
    def process(self, input_path: Union[str, Path], output_path: Union[str, Path], **kwargs) -> Dict[str, Any]:
        """
        Process audio file or directory for speech recognition.
        
        Parameters
        ----------
        input_path : str or Path
            Path to input audio file or directory
        output_path : str or Path
            Path to output file or directory
        **kwargs
            Additional processing parameters:
            - format : str, default='txt'
                Output format (txt, json, csv, docx)
            - with_timestamps : bool, default=False
                Include timestamps in output
            - with_confidence : bool, default=False
                Include confidence scores
            - language : str, optional
                Override language setting
            
        Returns
        -------
        dict
            ASR processing results and metadata
        """
        input_path = self.validate_input_path(input_path)
        output_path = Path(output_path)
        
        if input_path.is_file():
            return self._process_single_file(input_path, output_path, **kwargs)
        elif input_path.is_dir():
            return self._process_directory(input_path, output_path, **kwargs)
        else:
            raise ProcessingError(f"Invalid input path: {input_path}")
    
    def _process_single_file(self, input_path: Path, output_path: Path, **kwargs) -> Dict[str, Any]:
        """
        Process a single audio file.
        
        Parameters
        ----------
        input_path : Path
            Input audio file
        output_path : Path
            Output file path
        **kwargs
            Additional parameters
            
        Returns
        -------
        dict
            Processing results
        """
        self.ensure_output_dir(output_path.parent)
        
        try:
            self.logger.info(f"Processing ASR for {input_path.name}")
            start_time = time.time()
            
            # Transcribe audio
            result = self.whisper_asr.transcribe(
                str(input_path),
                language=kwargs.get('language', self.asr_config.language),
                **kwargs
            )
            
            # Add confidence scores if requested
            if kwargs.get('with_confidence', False):
                result = self.confidence_scorer.add_confidence_scores(result)
            
            # Add metadata
            processing_time = time.time() - start_time
            result['metadata'] = {
                'input_file': str(input_path),
                'model': self.asr_config.model_name,
                'language': result.get('language', self.asr_config.language),
                'processing_time': processing_time,
                'device': self.device
            }
            
            # Save in requested format
            output_format = kwargs.get('format', 'txt')
            self._save_transcription(result, output_path, output_format, **kwargs)
            
            self.logger.info(f"ASR completed in {processing_time:.2f}s")
            
            return {
                "input_file": str(input_path),
                "output_file": str(output_path),
                "transcription": result['text'],
                "language": result.get('language'),
                "segments": len(result.get('segments', [])),
                "processing_time": processing_time,
                "format": output_format,
                "success": True
            }
            
        except Exception as e:
            error_msg = f"ASR failed for {input_path.name}: {str(e)}"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg) from e
    
    def _process_directory(self, input_dir: Path, output_dir: Path, **kwargs) -> Dict[str, Any]:
        """
        Process all audio files in a directory.
        
        Parameters
        ----------
        input_dir : Path
            Input directory
        output_dir : Path
            Output directory
        **kwargs
            Additional parameters
            
        Returns
        -------
        dict
            Batch processing results
        """
        self.ensure_output_dir(output_dir)
        
        audio_files = self.filter_audio_files(input_dir)
        if not audio_files:
            self.logger.warning(f"No audio files found in {input_dir}")
            return {"processed_files": [], "errors": []}
        
        results = {"processed_files": [], "errors": []}
        
        for audio_file in audio_files:
            try:
                # Determine output file path
                output_format = kwargs.get('format', 'txt')
                output_file = output_dir / f"{audio_file.stem}.{output_format}"
                
                result = self._process_single_file(audio_file, output_file, **kwargs)
                results["processed_files"].append(result)
                
            except Exception as e:
                error_msg = f"Error processing {audio_file.name}: {str(e)}"
                self.logger.error(error_msg)
                results["errors"].append({
                    "file": str(audio_file),
                    "error": error_msg
                })
        
        return results
    
    def _save_transcription(self, result: Dict[str, Any], output_path: Path, 
                          output_format: str, **kwargs) -> None:
        """
        Save transcription in the specified format.
        
        Parameters
        ----------
        result : dict
            Whisper transcription result
        output_path : Path
            Output file path
        output_format : str
            Output format (txt, json, csv, docx)
        **kwargs
            Additional parameters
        """
        # Ensure correct file extension
        if not output_path.suffix:
            output_path = output_path.with_suffix(f'.{output_format}')
        
        if output_format == 'txt':
            self._save_txt(result, output_path, **kwargs)
        elif output_format == 'json':
            self._save_json(result, output_path, **kwargs)
        elif output_format == 'csv':
            self._save_csv(result, output_path, **kwargs)
        elif output_format == 'docx':
            self._save_docx(result, output_path, **kwargs)
        else:
            raise ValueError(f"Unsupported output format: {output_format}")
    
    def _save_txt(self, result: Dict[str, Any], output_path: Path, **kwargs) -> None:
        """Save transcription as plain text."""
        with_timestamps = kwargs.get('with_timestamps', False)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            if with_timestamps and 'segments' in result:
                for segment in result['segments']:
                    start_time = self._format_timestamp(segment['start'])
                    end_time = self._format_timestamp(segment['end'])
                    f.write(f"[{start_time} - {end_time}] {segment['text'].strip()}\n")
            else:
                f.write(result['text'])
    
    def _save_json(self, result: Dict[str, Any], output_path: Path, **kwargs) -> None:
        """Save transcription as JSON."""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
    
    def _save_csv(self, result: Dict[str, Any], output_path: Path, **kwargs) -> None:
        """Save transcription as CSV."""
        import pandas as pd
        
        if 'segments' not in result:
            # Create single row for full transcription
            df = pd.DataFrame([{
                'start_time': 0.0,
                'end_time': 0.0,
                'text': result['text'],
                'confidence': result.get('confidence', 0.0)
            }])
        else:
            # Create row for each segment
            segments_data = []
            for segment in result['segments']:
                segments_data.append({
                    'start_time': segment['start'],
                    'end_time': segment['end'],
                    'text': segment['text'].strip(),
                    'confidence': segment.get('confidence', 0.0)
                })
            df = pd.DataFrame(segments_data)
        
        df.to_csv(output_path, index=False)
    
    def _save_docx(self, result: Dict[str, Any], output_path: Path, **kwargs) -> None:
        """Save transcription as Word document."""
        doc = Document()
        
        # Add title
        title = doc.add_heading('Speech Transcription Report', 0)
        
        # Add metadata
        metadata_para = doc.add_paragraph()
        metadata = result.get('metadata', {})
        metadata_para.add_run('File: ').bold = True
        metadata_para.add_run(f"{metadata.get('input_file', 'Unknown')}\n")
        metadata_para.add_run('Model: ').bold = True
        metadata_para.add_run(f"{metadata.get('model', 'Unknown')}\n")
        metadata_para.add_run('Language: ').bold = True
        metadata_para.add_run(f"{metadata.get('language', 'Unknown')}\n")
        metadata_para.add_run('Processing Time: ').bold = True
        metadata_para.add_run(f"{metadata.get('processing_time', 0):.2f}s\n")
        
        # Add transcription
        doc.add_heading('Transcription', level=1)
        
        if 'segments' in result and kwargs.get('with_timestamps', False):
            for segment in result['segments']:
                para = doc.add_paragraph()
                start_time = self._format_timestamp(segment['start'])
                end_time = self._format_timestamp(segment['end'])
                para.add_run(f"[{start_time} - {end_time}] ").bold = True
                para.add_run(segment['text'].strip())
        else:
            doc.add_paragraph(result['text'])
        
        doc.save(output_path)
    
    def _format_timestamp(self, seconds: float) -> str:
        """Format timestamp in MM:SS format."""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"
    
    def extract_keywords(self, text: str, keyword_list: List[str], **kwargs) -> List[Dict[str, Any]]:
        """
        Extract keywords from transcribed text.
        
        Parameters
        ----------
        text : str
            Transcribed text
        keyword_list : list
            List of keywords to search for
        **kwargs
            Additional parameters for keyword extraction
            
        Returns
        -------
        list
            List of found keywords with metadata
        """
        from ..keywords import KeywordExtractor
        
        extractor = KeywordExtractor(logger=self.logger)
        return extractor.extract_keywords(text, keyword_list, **kwargs)
    
    def get_transcription_statistics(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate statistics for transcription result.
        
        Parameters
        ----------
        result : dict
            Whisper transcription result
            
        Returns
        -------
        dict
            Transcription statistics
        """
        stats = {
            "total_text_length": len(result['text']),
            "word_count": len(result['text'].split()),
            "language": result.get('language', 'unknown')
        }
        
        if 'segments' in result:
            segments = result['segments']
            stats.update({
                "num_segments": len(segments),
                "total_duration": segments[-1]['end'] if segments else 0,
                "avg_segment_duration": sum(seg['end'] - seg['start'] for seg in segments) / len(segments) if segments else 0
            })
            
            if 'confidence' in segments[0]:
                confidences = [seg['confidence'] for seg in segments]
                stats.update({
                    "avg_confidence": sum(confidences) / len(confidences),
                    "min_confidence": min(confidences),
                    "max_confidence": max(confidences)
                })
        
        return stats
