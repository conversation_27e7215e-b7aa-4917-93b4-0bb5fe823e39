"""
Whisper ASR implementation for AudioTechEquity pipeline.
"""

import logging
from typing import Dict, Any, Union, Optional
import torch
import whisper
import numpy as np

from ..core.exceptions import ModelLoadError, ProcessingError


class WhisperASR:
    """
    Whisper ASR implementation with optimizations for child speech.
    """
    
    def __init__(self, model_name: str = "turbo", device: str = "auto", 
                 logger: Optional[logging.Logger] = None):
        """
        Initialize Whisper ASR.
        
        Parameters
        ----------
        model_name : str
            Whisper model name (tiny, base, small, medium, large, turbo)
        device : str
            Device to use (auto, cpu, cuda)
        logger : logging.Logger, optional
            Logger instance
        """
        self.model_name = model_name
        self.device = self._get_device(device)
        self.logger = logger or logging.getLogger(__name__)
        
        # Load model
        self.model = self._load_model()
    
    def _get_device(self, device: str) -> str:
        """Get appropriate device for processing."""
        if device == "auto":
            return "cuda" if torch.cuda.is_available() else "cpu"
        return device
    
    def _load_model(self) -> whisper.Whisper:
        """Load Whisper model."""
        try:
            self.logger.info(f"Loading Whisper model: {self.model_name}")
            
            # Load model with appropriate settings
            model = whisper.load_model(
                self.model_name,
                device=self.device,
                download_root=None  # Use default cache
            )
            
            # Set to evaluation mode
            model.eval()
            
            self.logger.info(f"Whisper model loaded successfully on {self.device}")
            return model
            
        except Exception as e:
            raise ModelLoadError(f"Failed to load Whisper model {self.model_name}: {str(e)}") from e
    
    def transcribe(self, audio_path: str, language: str = "en", **kwargs) -> Dict[str, Any]:
        """
        Transcribe audio file using Whisper.
        
        Parameters
        ----------
        audio_path : str
            Path to audio file
        language : str
            Language code (e.g., 'en', 'es')
        **kwargs
            Additional Whisper parameters:
            - temperature : float, default=0.0
            - best_of : int, default=5
            - beam_size : int, default=5
            - fp16 : bool, default=True (if CUDA available)
            - word_timestamps : bool, default=False
            
        Returns
        -------
        dict
            Transcription result with text, segments, and metadata
        """
        try:
            self.logger.debug(f"Transcribing audio: {audio_path}")
            
            # Prepare transcription options
            options = self._prepare_transcription_options(language, **kwargs)
            
            # Transcribe
            result = self.model.transcribe(audio_path, **options)
            
            # Post-process result
            processed_result = self._post_process_result(result, **kwargs)
            
            self.logger.debug(f"Transcription completed: {len(processed_result['text'])} characters")
            
            return processed_result
            
        except Exception as e:
            raise ProcessingError(f"Transcription failed for {audio_path}: {str(e)}") from e
    
    def _prepare_transcription_options(self, language: str, **kwargs) -> Dict[str, Any]:
        """
        Prepare transcription options for Whisper.
        
        Parameters
        ----------
        language : str
            Language code
        **kwargs
            Additional options
            
        Returns
        -------
        dict
            Whisper transcription options
        """
        options = {
            "language": language,
            "task": "transcribe",  # Always transcribe (not translate)
            "temperature": kwargs.get("temperature", 0.0),
            "best_of": kwargs.get("best_of", 5),
            "beam_size": kwargs.get("beam_size", 5),
            "patience": kwargs.get("patience", 1.0),
            "length_penalty": kwargs.get("length_penalty", 1.0),
            "suppress_tokens": kwargs.get("suppress_tokens", "-1"),
            "initial_prompt": kwargs.get("initial_prompt", None),
            "condition_on_previous_text": kwargs.get("condition_on_previous_text", True),
            "fp16": kwargs.get("fp16", self.device == "cuda"),
            "compression_ratio_threshold": kwargs.get("compression_ratio_threshold", 2.4),
            "logprob_threshold": kwargs.get("logprob_threshold", -1.0),
            "no_speech_threshold": kwargs.get("no_speech_threshold", 0.6),
            "word_timestamps": kwargs.get("word_timestamps", False)
        }
        
        return options
    
    def _post_process_result(self, result: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Post-process Whisper transcription result.
        
        Parameters
        ----------
        result : dict
            Raw Whisper result
        **kwargs
            Additional parameters
            
        Returns
        -------
        dict
            Processed result
        """
        processed_result = {
            "text": result["text"].strip(),
            "language": result.get("language", "unknown")
        }
        
        # Add segments if available
        if "segments" in result:
            processed_segments = []
            for segment in result["segments"]:
                processed_segment = {
                    "start": segment["start"],
                    "end": segment["end"],
                    "text": segment["text"].strip()
                }
                
                # Add word-level timestamps if available
                if "words" in segment:
                    processed_segment["words"] = segment["words"]
                
                # Add confidence if available (from logprobs)
                if "avg_logprob" in segment:
                    # Convert log probability to confidence score (0-1)
                    confidence = np.exp(segment["avg_logprob"])
                    processed_segment["confidence"] = min(confidence, 1.0)
                
                processed_segments.append(processed_segment)
            
            processed_result["segments"] = processed_segments
        
        return processed_result
    
    def transcribe_with_vad(self, audio_path: str, vad_segments: list, **kwargs) -> Dict[str, Any]:
        """
        Transcribe audio using VAD segments for better accuracy.
        
        Parameters
        ----------
        audio_path : str
            Path to audio file
        vad_segments : list
            List of VAD segments with start/end times
        **kwargs
            Additional transcription parameters
            
        Returns
        -------
        dict
            Combined transcription result
        """
        try:
            import librosa
            
            # Load full audio
            audio, sr = librosa.load(audio_path, sr=16000)
            
            all_segments = []
            full_text = ""
            
            for i, vad_segment in enumerate(vad_segments):
                start_time = vad_segment['start']
                end_time = vad_segment['end']
                
                # Extract audio segment
                start_sample = int(start_time * sr)
                end_sample = int(end_time * sr)
                segment_audio = audio[start_sample:end_sample]
                
                # Save temporary segment
                import tempfile
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                    import soundfile as sf
                    sf.write(temp_file.name, segment_audio, sr)
                    
                    # Transcribe segment
                    segment_result = self.transcribe(temp_file.name, **kwargs)
                    
                    # Adjust timestamps to match original audio
                    if "segments" in segment_result:
                        for seg in segment_result["segments"]:
                            seg["start"] += start_time
                            seg["end"] += start_time
                        all_segments.extend(segment_result["segments"])
                    else:
                        # Create segment from full text
                        all_segments.append({
                            "start": start_time,
                            "end": end_time,
                            "text": segment_result["text"]
                        })
                    
                    full_text += " " + segment_result["text"]
                    
                    # Clean up temporary file
                    import os
                    os.unlink(temp_file.name)
            
            return {
                "text": full_text.strip(),
                "segments": all_segments,
                "language": kwargs.get("language", "en")
            }
            
        except Exception as e:
            raise ProcessingError(f"VAD-based transcription failed: {str(e)}") from e
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded model.
        
        Returns
        -------
        dict
            Model information
        """
        return {
            "model_name": self.model_name,
            "device": self.device,
            "model_size": self._get_model_size(),
            "parameters": self._get_parameter_count(),
            "languages": self._get_supported_languages()
        }
    
    def _get_model_size(self) -> str:
        """Get model size category."""
        size_map = {
            "tiny": "39M parameters",
            "base": "74M parameters", 
            "small": "244M parameters",
            "medium": "769M parameters",
            "large": "1550M parameters",
            "large-v2": "1550M parameters",
            "large-v3": "1550M parameters",
            "turbo": "809M parameters"
        }
        return size_map.get(self.model_name, "Unknown")
    
    def _get_parameter_count(self) -> int:
        """Get approximate parameter count."""
        if hasattr(self.model, 'parameters'):
            return sum(p.numel() for p in self.model.parameters())
        return 0
    
    def _get_supported_languages(self) -> list:
        """Get list of supported languages."""
        # Whisper supports 99 languages
        return list(whisper.tokenizer.LANGUAGES.keys())
    
    def benchmark_model(self, test_audio_path: str, num_runs: int = 3) -> Dict[str, Any]:
        """
        Benchmark model performance on test audio.
        
        Parameters
        ----------
        test_audio_path : str
            Path to test audio file
        num_runs : int
            Number of benchmark runs
            
        Returns
        -------
        dict
            Benchmark results
        """
        import time
        
        times = []
        
        for i in range(num_runs):
            start_time = time.time()
            result = self.transcribe(test_audio_path)
            end_time = time.time()
            
            processing_time = end_time - start_time
            times.append(processing_time)
            
            self.logger.debug(f"Benchmark run {i+1}: {processing_time:.2f}s")
        
        # Calculate audio duration
        import librosa
        audio, sr = librosa.load(test_audio_path)
        audio_duration = len(audio) / sr
        
        avg_time = sum(times) / len(times)
        realtime_factor = avg_time / audio_duration
        
        return {
            "audio_duration": audio_duration,
            "processing_times": times,
            "average_processing_time": avg_time,
            "min_processing_time": min(times),
            "max_processing_time": max(times),
            "realtime_factor": realtime_factor,
            "model_name": self.model_name,
            "device": self.device
        }
