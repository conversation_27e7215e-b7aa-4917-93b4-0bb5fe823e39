"""
Audio segmentation processor for extracting speaker segments from diarization results.
"""

import os
import json
import pandas as pd
import numpy as np
from typing import Dict, Any, Union, Optional, List
from pathlib import Path

import torchaudio
from pydub import AudioSegment

from ..core.base import BaseProcessor
from ..core.config import SegmentationConfig
from ..core.exceptions import ProcessingError, ValidationError


class AudioSegmenter(BaseProcessor):
    """
    Audio segmentation processor.
    
    Extracts speaker segments from diarization results and applies processing
    such as padding, crossfade, and chunking.
    """
    
    def __init__(self, config: Optional[SegmentationConfig] = None, **kwargs):
        """
        Initialize the audio segmenter.
        
        Parameters
        ----------
        config : SegmentationConfig, optional
            Segmentation configuration
        **kwargs
            Additional arguments passed to base class
        """
        super().__init__(config=config.__dict__ if config else None, **kwargs)
        
        self.segmentation_config = config or SegmentationConfig()
    
    def process(self, input_path: Union[str, Path], output_path: Union[str, Path], 
                diarization_path: Union[str, Path], **kwargs) -> Dict[str, Any]:
        """
        Process audio file to extract speaker segments based on diarization results.
        
        Parameters
        ----------
        input_path : str or Path
            Path to input audio file
        output_path : str or Path
            Path to output directory for segments
        diarization_path : str or Path
            Path to diarization results (CSV file or directory)
        **kwargs
            Additional processing parameters:
            - extract_speaker : str, optional
                Specific speaker to extract (most active if not specified)
            - apply_padding : bool, default=True
                Whether to apply padding to segments
            - apply_crossfade : bool, default=False
                Whether to apply crossfade between segments
            
        Returns
        -------
        dict
            Segmentation results and metadata
        """
        input_path = self.validate_input_path(input_path)
        output_path = self.ensure_output_dir(output_path)
        diarization_path = self.validate_input_path(diarization_path)
        
        try:
            self.logger.info(f"Processing segmentation for {input_path.name}")
            
            # Load diarization results
            segments = self._load_diarization_results(diarization_path)
            
            # Load audio
            audio = AudioSegment.from_file(str(input_path))
            
            # Determine target speaker
            target_speaker = kwargs.get('extract_speaker')
            if target_speaker is None:
                target_speaker = self._find_most_active_speaker(segments)
                self.logger.info(f"Auto-selected most active speaker: {target_speaker}")
            
            # Filter segments for target speaker
            speaker_segments = [seg for seg in segments if seg['speaker'] == target_speaker]
            
            if not speaker_segments:
                raise ProcessingError(f"No segments found for speaker: {target_speaker}")
            
            # Extract and process segments
            extracted_segments = self._extract_segments(
                audio, speaker_segments, output_path, **kwargs
            )
            
            # Apply chunking if needed
            if self.segmentation_config.max_segment_length > 0:
                chunked_segments = self._apply_chunking(extracted_segments, output_path)
                extracted_segments.extend(chunked_segments)
            
            self.logger.info(f"Segmentation completed: {len(extracted_segments)} segments extracted")
            
            return {
                "input_file": str(input_path),
                "output_dir": str(output_path),
                "diarization_file": str(diarization_path),
                "target_speaker": target_speaker,
                "total_segments": len(speaker_segments),
                "extracted_segments": len(extracted_segments),
                "segments": extracted_segments,
                "success": True
            }
            
        except Exception as e:
            error_msg = f"Segmentation failed for {input_path.name}: {str(e)}"
            self.logger.error(error_msg)
            raise ProcessingError(error_msg) from e
    
    def _load_diarization_results(self, diarization_path: Path) -> List[Dict[str, Any]]:
        """
        Load diarization results from CSV file or directory.
        
        Parameters
        ----------
        diarization_path : Path
            Path to diarization results
            
        Returns
        -------
        list
            List of diarization segments
        """
        if diarization_path.is_file() and diarization_path.suffix == '.csv':
            # Load from CSV file
            df = pd.read_csv(diarization_path)
            segments = []
            
            for _, row in df.iterrows():
                segments.append({
                    'start_time': row['start_time'],
                    'end_time': row['end_time'],
                    'duration': row['duration'],
                    'speaker': row['speaker']
                })
            
            return segments
            
        elif diarization_path.is_dir():
            # Look for CSV files in directory
            csv_files = list(diarization_path.glob("*.csv"))
            if not csv_files:
                raise ValidationError(f"No CSV files found in {diarization_path}")
            
            # Use the first CSV file found
            return self._load_diarization_results(csv_files[0])
        
        else:
            raise ValidationError(f"Invalid diarization path: {diarization_path}")
    
    def _find_most_active_speaker(self, segments: List[Dict[str, Any]]) -> str:
        """
        Find the speaker with the most total speaking time.
        
        Parameters
        ----------
        segments : list
            List of diarization segments
            
        Returns
        -------
        str
            Most active speaker ID
        """
        speaker_durations = {}
        
        for segment in segments:
            speaker = segment['speaker']
            duration = segment['duration']
            
            if speaker not in speaker_durations:
                speaker_durations[speaker] = 0
            speaker_durations[speaker] += duration
        
        if not speaker_durations:
            raise ProcessingError("No speakers found in diarization results")
        
        most_active_speaker = max(speaker_durations.items(), key=lambda x: x[1])[0]
        return most_active_speaker
    
    def _extract_segments(self, audio: AudioSegment, segments: List[Dict[str, Any]], 
                         output_dir: Path, **kwargs) -> List[Dict[str, Any]]:
        """
        Extract audio segments for a specific speaker.
        
        Parameters
        ----------
        audio : AudioSegment
            Original audio
        segments : list
            Speaker segments to extract
        output_dir : Path
            Output directory
        **kwargs
            Additional parameters
            
        Returns
        -------
        list
            List of extracted segment information
        """
        extracted_segments = []
        apply_padding = kwargs.get('apply_padding', True)
        apply_crossfade = kwargs.get('apply_crossfade', False)
        
        padding_ms = int(self.segmentation_config.padding_sec * 1000)
        crossfade_ms = int(self.segmentation_config.crossfade_sec * 1000)
        
        for i, segment in enumerate(segments):
            start_ms = int(segment['start_time'] * 1000)
            end_ms = int(segment['end_time'] * 1000)
            
            # Apply padding
            if apply_padding:
                padded_start = max(0, start_ms - padding_ms)
                padded_end = min(len(audio), end_ms + padding_ms)
            else:
                padded_start = start_ms
                padded_end = end_ms
            
            # Extract segment
            segment_audio = audio[padded_start:padded_end]
            
            # Apply crossfade if requested and not the first segment
            if apply_crossfade and i > 0 and self.segmentation_config.apply_crossfade:
                segment_audio = segment_audio.fade_in(crossfade_ms).fade_out(crossfade_ms)
            
            # Add noise if configured
            if self.segmentation_config.noise_amplitude > 0:
                segment_audio = self._add_noise(segment_audio)
            
            # Save segment
            segment_filename = f"segment_{i:03d}_{segment['speaker']}.wav"
            segment_path = output_dir / segment_filename
            segment_audio.export(str(segment_path), format="wav")
            
            extracted_segments.append({
                'segment_id': i,
                'speaker': segment['speaker'],
                'original_start': segment['start_time'],
                'original_end': segment['end_time'],
                'padded_start': padded_start / 1000.0,
                'padded_end': padded_end / 1000.0,
                'duration': len(segment_audio) / 1000.0,
                'file_path': str(segment_path),
                'padding_applied': apply_padding,
                'crossfade_applied': apply_crossfade
            })
        
        return extracted_segments
    
    def _apply_chunking(self, segments: List[Dict[str, Any]], output_dir: Path) -> List[Dict[str, Any]]:
        """
        Apply chunking to segments that exceed maximum length.
        
        Parameters
        ----------
        segments : list
            List of extracted segments
        output_dir : Path
            Output directory
            
        Returns
        -------
        list
            List of additional chunked segments
        """
        chunked_segments = []
        max_length = self.segmentation_config.max_segment_length
        
        for segment in segments:
            if segment['duration'] > max_length:
                # Load the segment audio
                segment_audio = AudioSegment.from_file(segment['file_path'])
                
                # Calculate number of chunks needed
                num_chunks = int(np.ceil(segment['duration'] / max_length))
                chunk_duration_ms = int(max_length * 1000)
                
                for chunk_idx in range(num_chunks):
                    start_ms = chunk_idx * chunk_duration_ms
                    end_ms = min(start_ms + chunk_duration_ms, len(segment_audio))
                    
                    chunk_audio = segment_audio[start_ms:end_ms]
                    
                    # Save chunk
                    chunk_filename = f"chunk_{segment['segment_id']:03d}_{chunk_idx:02d}_{segment['speaker']}.wav"
                    chunk_path = output_dir / chunk_filename
                    chunk_audio.export(str(chunk_path), format="wav")
                    
                    chunked_segments.append({
                        'segment_id': f"{segment['segment_id']}_{chunk_idx}",
                        'parent_segment': segment['segment_id'],
                        'chunk_index': chunk_idx,
                        'speaker': segment['speaker'],
                        'start_time': segment['padded_start'] + (start_ms / 1000.0),
                        'end_time': segment['padded_start'] + (end_ms / 1000.0),
                        'duration': len(chunk_audio) / 1000.0,
                        'file_path': str(chunk_path),
                        'is_chunk': True
                    })
        
        return chunked_segments
    
    def _add_noise(self, audio: AudioSegment) -> AudioSegment:
        """
        Add low-level noise to audio segment.
        
        Parameters
        ----------
        audio : AudioSegment
            Input audio segment
            
        Returns
        -------
        AudioSegment
            Audio with added noise
        """
        # Generate white noise
        noise_amplitude = self.segmentation_config.noise_amplitude
        samples = np.array(audio.get_array_of_samples())
        
        if audio.channels == 2:
            samples = samples.reshape((-1, 2))
        
        # Add noise
        noise = np.random.normal(0, noise_amplitude * np.max(np.abs(samples)), samples.shape)
        noisy_samples = samples + noise.astype(samples.dtype)
        
        # Convert back to AudioSegment
        noisy_audio = audio._spawn(noisy_samples.tobytes())
        return noisy_audio
    
    def get_segment_statistics(self, segments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate statistics for extracted segments.
        
        Parameters
        ----------
        segments : list
            List of extracted segments
            
        Returns
        -------
        dict
            Segment statistics
        """
        if not segments:
            return {"total_segments": 0, "total_duration": 0}
        
        durations = [seg['duration'] for seg in segments]
        speakers = [seg['speaker'] for seg in segments]
        
        return {
            "total_segments": len(segments),
            "total_duration": sum(durations),
            "average_duration": np.mean(durations),
            "min_duration": min(durations),
            "max_duration": max(durations),
            "unique_speakers": len(set(speakers)),
            "speaker_counts": {speaker: speakers.count(speaker) for speaker in set(speakers)}
        }
