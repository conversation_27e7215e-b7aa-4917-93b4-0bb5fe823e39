"""
Audio chunking utilities for segmentation.
"""

import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

import numpy as np
from pydub import AudioSegment


class AudioChunker:
    """
    Utility class for chunking audio segments.
    """
    
    def __init__(self, max_chunk_length: float = 30.0, overlap_sec: float = 0.0,
                 logger: Optional[logging.Logger] = None):
        """
        Initialize audio chunker.
        
        Parameters
        ----------
        max_chunk_length : float
            Maximum chunk length in seconds
        overlap_sec : float
            Overlap between chunks in seconds
        logger : logging.Logger, optional
            Logger instance
        """
        self.max_chunk_length = max_chunk_length
        self.overlap_sec = overlap_sec
        self.logger = logger or logging.getLogger(__name__)
    
    def chunk_audio_segment(self, audio: AudioSegment, output_dir: Path, 
                           base_filename: str) -> List[Dict[str, Any]]:
        """
        Chunk an audio segment into smaller pieces.
        
        Parameters
        ----------
        audio : AudioSegment
            Audio segment to chunk
        output_dir : Path
            Output directory for chunks
        base_filename : str
            Base filename for chunks
            
        Returns
        -------
        list
            List of chunk information
        """
        chunks = []
        audio_duration = len(audio) / 1000.0  # Convert to seconds
        
        if audio_duration <= self.max_chunk_length:
            # No chunking needed
            chunk_path = output_dir / f"{base_filename}.wav"
            audio.export(str(chunk_path), format="wav")
            
            chunks.append({
                'chunk_index': 0,
                'start_time': 0.0,
                'end_time': audio_duration,
                'duration': audio_duration,
                'file_path': str(chunk_path),
                'is_chunk': False
            })
            
            return chunks
        
        # Calculate chunk parameters
        chunk_length_ms = int(self.max_chunk_length * 1000)
        overlap_ms = int(self.overlap_sec * 1000)
        step_ms = chunk_length_ms - overlap_ms
        
        chunk_index = 0
        start_ms = 0
        
        while start_ms < len(audio):
            end_ms = min(start_ms + chunk_length_ms, len(audio))
            
            # Extract chunk
            chunk_audio = audio[start_ms:end_ms]
            
            # Save chunk
            chunk_filename = f"{base_filename}_chunk_{chunk_index:03d}.wav"
            chunk_path = output_dir / chunk_filename
            chunk_audio.export(str(chunk_path), format="wav")
            
            # Calculate timing
            start_time = start_ms / 1000.0
            end_time = end_ms / 1000.0
            duration = (end_ms - start_ms) / 1000.0
            
            chunks.append({
                'chunk_index': chunk_index,
                'start_time': start_time,
                'end_time': end_time,
                'duration': duration,
                'file_path': str(chunk_path),
                'is_chunk': True,
                'overlap_start': start_ms > 0 and self.overlap_sec > 0,
                'overlap_end': end_ms < len(audio) and self.overlap_sec > 0
            })
            
            chunk_index += 1
            start_ms += step_ms
        
        self.logger.info(f"Created {len(chunks)} chunks from {audio_duration:.2f}s audio")
        
        return chunks
    
    def merge_chunks(self, chunks: List[Dict[str, Any]], output_path: Path) -> Dict[str, Any]:
        """
        Merge audio chunks back into a single file.
        
        Parameters
        ----------
        chunks : list
            List of chunk information
        output_path : Path
            Output path for merged audio
            
        Returns
        -------
        dict
            Merge result information
        """
        if not chunks:
            raise ValueError("No chunks to merge")
        
        # Sort chunks by start time
        sorted_chunks = sorted(chunks, key=lambda x: x['start_time'])
        
        # Load and merge audio
        merged_audio = AudioSegment.empty()
        
        for i, chunk in enumerate(sorted_chunks):
            chunk_audio = AudioSegment.from_file(chunk['file_path'])
            
            if i == 0:
                # First chunk - add as is
                merged_audio += chunk_audio
            else:
                # Subsequent chunks - handle overlap
                if self.overlap_sec > 0 and chunk.get('overlap_start', False):
                    # Remove overlap from beginning of chunk
                    overlap_ms = int(self.overlap_sec * 1000)
                    chunk_audio = chunk_audio[overlap_ms:]
                
                merged_audio += chunk_audio
        
        # Export merged audio
        merged_audio.export(str(output_path), format="wav")
        
        total_duration = len(merged_audio) / 1000.0
        
        return {
            'output_path': str(output_path),
            'total_chunks': len(chunks),
            'total_duration': total_duration,
            'success': True
        }
    
    def get_optimal_chunk_size(self, audio_duration: float, target_chunks: int) -> float:
        """
        Calculate optimal chunk size for a given audio duration and target number of chunks.
        
        Parameters
        ----------
        audio_duration : float
            Total audio duration in seconds
        target_chunks : int
            Target number of chunks
            
        Returns
        -------
        float
            Optimal chunk size in seconds
        """
        if target_chunks <= 1:
            return audio_duration
        
        # Account for overlap
        effective_overlap = self.overlap_sec * (target_chunks - 1)
        effective_duration = audio_duration + effective_overlap
        
        optimal_chunk_size = effective_duration / target_chunks
        
        # Ensure it doesn't exceed maximum
        return min(optimal_chunk_size, self.max_chunk_length)
