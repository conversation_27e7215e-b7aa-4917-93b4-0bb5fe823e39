"""
Speaker extraction utilities for segmentation.
"""

import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

import pandas as pd
import numpy as np


class SpeakerExtractor:
    """
    Utility class for extracting speaker-specific segments from diarization results.
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        Initialize speaker extractor.
        
        Parameters
        ----------
        logger : logging.Logger, optional
            Logger instance
        """
        self.logger = logger or logging.getLogger(__name__)
    
    def extract_speaker_segments(self, diarization_results: List[Dict[str, Any]], 
                                target_speaker: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Extract segments for a specific speaker.
        
        Parameters
        ----------
        diarization_results : list
            List of diarization segments
        target_speaker : str, optional
            Target speaker ID. If None, extracts most active speaker
            
        Returns
        -------
        list
            List of segments for the target speaker
        """
        if not diarization_results:
            return []
        
        # Determine target speaker if not specified
        if target_speaker is None:
            target_speaker = self.find_most_active_speaker(diarization_results)
            self.logger.info(f"Auto-selected most active speaker: {target_speaker}")
        
        # Filter segments for target speaker
        speaker_segments = [
            segment for segment in diarization_results 
            if segment.get('speaker') == target_speaker
        ]
        
        return speaker_segments
    
    def find_most_active_speaker(self, diarization_results: List[Dict[str, Any]]) -> str:
        """
        Find the speaker with the most total speaking time.
        
        Parameters
        ----------
        diarization_results : list
            List of diarization segments
            
        Returns
        -------
        str
            Most active speaker ID
        """
        speaker_durations = {}
        
        for segment in diarization_results:
            speaker = segment.get('speaker')
            duration = segment.get('duration', 0)
            
            if speaker not in speaker_durations:
                speaker_durations[speaker] = 0
            speaker_durations[speaker] += duration
        
        if not speaker_durations:
            raise ValueError("No speakers found in diarization results")
        
        most_active_speaker = max(speaker_durations.items(), key=lambda x: x[1])[0]
        return most_active_speaker
    
    def get_speaker_statistics(self, diarization_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate speaker statistics from diarization results.
        
        Parameters
        ----------
        diarization_results : list
            List of diarization segments
            
        Returns
        -------
        dict
            Speaker statistics
        """
        if not diarization_results:
            return {"total_speakers": 0, "total_duration": 0}
        
        speaker_stats = {}
        total_duration = 0
        
        for segment in diarization_results:
            speaker = segment.get('speaker')
            duration = segment.get('duration', 0)
            
            if speaker not in speaker_stats:
                speaker_stats[speaker] = {
                    'total_duration': 0,
                    'segment_count': 0,
                    'segments': []
                }
            
            speaker_stats[speaker]['total_duration'] += duration
            speaker_stats[speaker]['segment_count'] += 1
            speaker_stats[speaker]['segments'].append(segment)
            total_duration += duration
        
        # Calculate percentages and additional metrics
        for speaker, stats in speaker_stats.items():
            stats['percentage'] = (stats['total_duration'] / total_duration * 100) if total_duration > 0 else 0
            stats['average_segment_duration'] = stats['total_duration'] / stats['segment_count'] if stats['segment_count'] > 0 else 0
        
        return {
            "total_speakers": len(speaker_stats),
            "total_duration": total_duration,
            "speaker_statistics": speaker_stats,
            "most_active_speaker": max(speaker_stats.items(), key=lambda x: x[1]['total_duration'])[0] if speaker_stats else None
        }
