#!/bin/sh

set -e

generate_docker() {
  docker run --rm kaczmarj/neurodocker:0.7.0 generate docker \
             --base ubuntu:22.04 \
             --pkg-manager apt \
             --env DEBIAN_FRONTEND=noninteractive \
             --miniconda \
               version=latest \
               conda_install="python=3.11" \
               create_env='audtecheq' \
               activate=true \
             --copy . /home/<USER>
             --run-bash "conda activate audtecheq && cd /home/<USER>" \
             --env IS_DOCKER=1 \
             --workdir '/tmp/' \
             --entrypoint "/neurodocker/startup.sh audtecheq"
}

# generate files
generate_docker > Dockerfile

# check if images should be built locally or not
if [ "$1" = "local" ]; then
    echo "Docker image will be built locally"
    # build image using the saved files
    docker build -t audtecheq:local .
else
  echo "Image(s) won't be built locally."
fi
