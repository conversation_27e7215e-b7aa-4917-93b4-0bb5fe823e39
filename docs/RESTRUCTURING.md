# AudioTechEquity Codebase Restructuring Documentation

## Overview

This document details the comprehensive restructuring of the AudioTechEquity codebase from Jupyter notebook-based scripts to a modular, class-based architecture with command-line interface support.

## Table of Contents

1. [Restructuring Phases](#restructuring-phases)
2. [Architecture Changes](#architecture-changes)
3. [New Directory Structure](#new-directory-structure)
4. [Component Details](#component-details)
5. [Migration Guide](#migration-guide)
6. [Breaking Changes](#breaking-changes)

## Restructuring Phases

### Phase 1: Core Infrastructure Setup

**Objective**: Establish foundational classes and configuration management

**Changes Made**:
- Created `audtecheq/core/` module with base classes
- Implemented `BaseProcessor` abstract class for consistent interfaces
- Added `BatchProcessor` for handling multiple files
- Created comprehensive configuration system with YAML support
- Implemented custom exception hierarchy
- Added centralized logging system

**Files Created**:
```
audtecheq/core/
├── __init__.py
├── base.py           # Base processor classes
├── config.py         # Configuration management
└── exceptions.py     # Custom exceptions
```

**Key Features**:
- Abstract base class ensuring consistent interfaces
- Automatic input/output validation
- Configurable logging levels
- YAML-based configuration with type safety
- Hierarchical exception handling

### Phase 2: Preprocessing Module

**Objective**: Convert notebook preprocessing code to modular classes

**Original Code Location**: `Diarizer code/vanilla.ipynb` (cells 1-10)

**Changes Made**:
- Extracted resampling logic into `AudioResampler` class
- Converted normalization code to `AudioNormalizer` class  
- Refactored denoising into `AudioDenoiser` class
- Created unified `AudioPreprocessor` combining all steps
- Added comprehensive error handling and progress tracking

**Files Created**:
```
audtecheq/preprocessing/
├── __init__.py
├── audio_processor.py  # Main preprocessing pipeline
├── resampler.py       # Audio resampling
├── normalizer.py      # Loudness normalization
└── denoiser.py        # Noise reduction
```

**Migration from Notebook**:
```python
# Old notebook code (simplified)
audio = AudioSegment.from_file(audio_path)
resampled_audio = audio.set_frame_rate(16000).set_channels(1)
# ... normalization and denoising steps

# New class-based approach
preprocessor = AudioPreprocessor(config=preprocess_config)
result = preprocessor.process(input_path, output_path)
```

### Phase 3: VAD Module

**Objective**: Convert VAD notebook code to production-ready classes

**Original Code Location**: `Diarizer code/vanilla.ipynb` (cells 11-15)

**Changes Made**:
- Implemented `VADProcessor` with Silero VAD integration
- Added fallback energy-based VAD for environments without NeMo
- Created `SilenceRemover` for post-processing
- Added speech activity analysis capabilities
- Integrated with NeMo VAD models when available

**Files Created**:
```
audtecheq/vad/
├── __init__.py
├── vad_processor.py    # Main VAD processing
└── silence_remover.py  # Silence removal utilities
```

**Key Improvements**:
- Model loading with graceful fallbacks
- Configurable VAD parameters
- JSON output for speech timestamps
- Batch processing support

### Phase 4: Diarization Module

**Objective**: Convert NeMo diarization code to structured classes

**Original Code Location**: `Diarizer code/vanilla.ipynb` (cells 16-25)

**Changes Made**:
- Created `SpeakerDiarizer` class wrapping NeMo ClusteringDiarizer
- Implemented automatic RTTM to CSV conversion
- Added diarization quality analysis
- Created speaker clustering utilities
- Added comprehensive statistics calculation

**Files Created**:
```
audtecheq/diarization/
├── __init__.py
├── diarizer.py        # Main diarization processor
├── clustering.py      # Speaker clustering utilities
└── testing.py         # Quality assessment tools
```

**Enhanced Features**:
- Automatic configuration generation for NeMo
- Quality scoring and recommendations
- Speaker balance analysis
- Configurable clustering backends

### Phase 5: Command Line Interface

**Objective**: Create professional CLI for all components

**Changes Made**:
- Implemented comprehensive argument parsing
- Created subcommands for each pipeline component
- Added full pipeline execution capability
- Integrated configuration file support
- Added batch processing options

**Files Created**:
```
audtecheq/cli/
├── __init__.py
├── main.py           # Main CLI entry point
└── commands.py       # Command implementations
```

**CLI Commands Available**:
- `audtecheq preprocess` - Audio preprocessing
- `audtecheq vad` - Voice activity detection
- `audtecheq diarize` - Speaker diarization
- `audtecheq segment` - Audio segmentation (planned)
- `audtecheq asr` - Speech recognition (planned)
- `audtecheq test` - Quality testing (planned)
- `audtecheq pipeline` - Complete end-to-end processing

### Phase 6: Package Integration

**Objective**: Update package structure and dependencies

**Changes Made**:
- Updated `audtecheq/__init__.py` with new exports
- Modified `setup.py` entry points for CLI
- Updated `requirements.txt` with all dependencies
- Created default configuration files
- Added comprehensive examples

**Files Modified**:
```
audtecheq/__init__.py     # Updated exports
setup.py                 # CLI entry point
requirements.txt         # New dependencies
```

**Files Created**:
```
audtecheq/config/default_config.yaml  # Default settings
examples/example_usage.py             # Usage examples
```

## Architecture Changes

### Before: Notebook-Based Architecture
```
Diarizer code/
├── vanilla.ipynb           # All processing logic
├── Copy_of_Diarizer_+_Fine_tuned_Whisper.ipynb
Whisper/
├── Open_whisper.ipynb      # ASR logic
Automated script to recognize keywords/
├── Data/automation.ipynb   # Keyword processing
```

### After: Modular Class-Based Architecture
```
audtecheq/
├── core/                   # Base classes and configuration
├── preprocessing/          # Audio preprocessing pipeline
├── vad/                   # Voice activity detection
├── diarization/           # Speaker diarization
├── segmentation/          # Audio segmentation (planned)
├── asr/                   # Speech recognition (planned)
├── keywords/              # Keyword analysis (planned)
├── testing/               # Quality assessment (planned)
├── cli/                   # Command line interface
├── config/                # Configuration files
└── utils/                 # Utility functions
```

## New Directory Structure

```
AudioTechEquity/
├── audtecheq/                          # Main package
│   ├── __init__.py                     # Package exports
│   ├── core/                           # Core infrastructure
│   │   ├── __init__.py
│   │   ├── base.py                     # Base processor classes
│   │   ├── config.py                   # Configuration management
│   │   └── exceptions.py               # Custom exceptions
│   ├── preprocessing/                  # Audio preprocessing
│   │   ├── __init__.py
│   │   ├── audio_processor.py          # Main preprocessing class
│   │   ├── resampler.py               # Audio resampling
│   │   ├── normalizer.py              # Loudness normalization
│   │   └── denoiser.py                # Noise reduction
│   ├── vad/                           # Voice Activity Detection
│   │   ├── __init__.py
│   │   ├── vad_processor.py           # VAD processing
│   │   └── silence_remover.py         # Silence removal
│   ├── diarization/                   # Speaker diarization
│   │   ├── __init__.py
│   │   ├── diarizer.py                # Main diarization class
│   │   ├── clustering.py              # Speaker clustering
│   │   └── testing.py                 # Quality assessment
│   ├── cli/                           # Command line interface
│   │   ├── __init__.py
│   │   ├── main.py                    # CLI entry point
│   │   └── commands.py                # Command implementations
│   ├── config/                        # Configuration files
│   │   └── default_config.yaml        # Default settings
│   └── utils/                         # Existing utilities
│       ├── __init__.py
│       └── validation.py
├── examples/                          # Usage examples
│   ├── README.rst
│   └── example_usage.py               # Comprehensive examples
├── docs/                              # Documentation
│   ├── RESTRUCTURING.md               # This document
│   └── source/                        # Sphinx documentation
├── requirements.txt                   # Updated dependencies
├── setup.py                          # Updated entry points
└── README.rst                        # Updated usage guide
```

## Component Details

### Core Infrastructure (`audtecheq/core/`)

**BaseProcessor Class**:
- Abstract base for all processing components
- Standardized `process()` method signature
- Built-in input validation and output directory creation
- Integrated logging and error handling

**Configuration System**:
- Type-safe configuration classes using dataclasses
- YAML serialization/deserialization
- Hierarchical configuration structure
- Environment-specific overrides

**Exception Hierarchy**:
```python
AudioTechEquityError
├── ProcessingError
│   ├── DiarizationError
│   ├── ASRError
│   └── SegmentationError
├── ValidationError
├── ConfigurationError
└── ModelLoadError
```

### Preprocessing Pipeline (`audtecheq/preprocessing/`)

**AudioPreprocessor**:
- Combines resampling, normalization, and denoising
- Supports step-by-step processing with intermediate outputs
- Configurable parameters for each processing step
- Batch processing capabilities

**Individual Components**:
- `AudioResampler`: Sample rate and channel conversion
- `AudioNormalizer`: dBFS-based loudness normalization
- `AudioDenoiser`: Spectral noise reduction with noisereduce

### VAD System (`audtecheq/vad/`)

**VADProcessor**:
- Silero VAD integration with NeMo fallback
- Configurable speech/silence thresholds
- JSON timestamp output
- Speech activity analysis

### Diarization System (`audtecheq/diarization/`)

**SpeakerDiarizer**:
- NeMo ClusteringDiarizer integration
- Automatic RTTM to CSV conversion
- Quality analysis and recommendations
- Configurable clustering algorithms

## Migration Guide

### From Notebook to Class-Based Usage

**Old Notebook Approach**:
```python
# Cell 1: Load and resample
audio = AudioSegment.from_file(audio_path)
resampled_audio = audio.set_frame_rate(16000).set_channels(1)

# Cell 2: Normalize
change_in_dBFS = target_dBFS - audio.dBFS
normalized_audio = audio.apply_gain(change_in_dBFS)

# Cell 3: Denoise
audio, sr = sf.read(input_path)
reduced_noise = nr.reduce_noise(y=audio, sr=sr)
```

**New Class-Based Approach**:
```python
from audtecheq import AudioPreprocessor, PreprocessingConfig

config = PreprocessingConfig(
    target_sample_rate=16000,
    target_channels=1,
    target_dbfs=-20.0,
    noise_reduction_prop=0.8
)

preprocessor = AudioPreprocessor(config=config)
result = preprocessor.process("input.wav", "output.wav")
```

### Configuration Migration

**Old Hardcoded Parameters**:
```python
target_sample_rate = 16000
target_channels = 1
target_dBFS = -20.0
prop_decrease = 0.8
```

**New Configuration System**:
```yaml
# config.yaml
preprocessing:
  target_sample_rate: 16000
  target_channels: 1
  target_dbfs: -20.0
  noise_reduction_prop: 0.8
```

```python
config = PipelineConfig.from_yaml("config.yaml")
```

## Breaking Changes

### Import Changes
```python
# Old (not available)
# from audtecheq import some_function

# New
from audtecheq import AudioPreprocessor, SpeakerDiarizer, PipelineConfig
from audtecheq.core import BaseProcessor
from audtecheq.preprocessing import AudioResampler
```

### CLI Changes
```bash
# Old (basic CLI)
audtecheq --input data/ --output results/

# New (component-specific)
audtecheq pipeline --input data/ --output results/
audtecheq preprocess --input audio.wav --output preprocessed.wav
audtecheq diarize --input audio.wav --output diarization/
```

### Configuration Changes
- Configuration now uses YAML files instead of hardcoded parameters
- Hierarchical configuration structure
- Type-safe configuration classes

### Dependencies
- Added: `torch`, `torchaudio`, `omegaconf`, `pyyaml`, `openai-whisper`
- Updated: `noisereduce`, `soundfile`, `pydub`
- Optional: `nemo_toolkit[asr]` for advanced diarization

## Benefits of Restructuring

1. **Modularity**: Components can be used independently
2. **Maintainability**: Clear separation of concerns
3. **Extensibility**: Easy to add new processors
4. **Testability**: Each component can be unit tested
5. **Usability**: Professional CLI and programmatic interfaces
6. **Configuration**: Flexible, file-based configuration
7. **Error Handling**: Comprehensive error reporting
8. **Documentation**: Self-documenting code structure

## Next Steps

The following components are planned for implementation following the same architectural patterns:

1. **Segmentation Module**: Extract and process speaker segments
2. **ASR Module**: Whisper-based speech recognition
3. **Testing Module**: Quality assessment and evaluation
4. **Keywords Module**: GFTA and word repetition analysis

Each will follow the established patterns:
- Inherit from `BaseProcessor`
- Use configuration dataclasses
- Provide both CLI and programmatic interfaces
- Include comprehensive error handling and logging
