# Automatic Speech Recognition (ASR)

## Table of Contents

1. [Overview](#overview)
2. [Our ASR Approach](#our-asr-approach)
3. [ASR Models Used](#asr-models-used)
4. [Fine-tuning Process](#fine-tuning-process)
5. [Metadata and Output Format](#metadata-and-output-format)
6. [Evaluation Benchmarks](#evaluation-benchmarks)
7. [Sample Transcripts](#sample-transcripts)
8. [Evaluation Summary](#evaluation-summary)
9. [Usage Examples](#usage-examples)

## Overview

Automatic Speech Recognition (ASR) is a technology that converts spoken language into written text by analyzing audio signals and recognizing speech patterns. It enables machines to understand human speech for use in applications like virtual assistants, transcription services, and voice-controlled systems.

### What is ASR?

ASR systems typically involve several key components:
- **Audio preprocessing**: Converting raw audio into features suitable for machine learning
- **Acoustic modeling**: Understanding the relationship between audio features and phonemes
- **Language modeling**: Predicting word sequences based on linguistic context
- **Decoding**: Combining acoustic and language models to produce the most likely text output

Modern ASR systems, particularly those based on deep learning, often integrate these components into end-to-end architectures that can be trained jointly.

## Our ASR Approach

### Project Goals

In our project, ASR serves a critical role in analyzing speech patterns and language development in children. Our specific objectives include:

1. **Child Speech Analysis**: Transcribing child speech for developmental assessment
2. **Confidence Scoring**: Providing reliability metrics for transcription quality
<TODO: Need to add/modify these objectives>

### Pipeline Integration

Our ASR component integrates seamlessly with the broader project pipeline:

```
Raw Audio → Preprocessing → VAD & Silence Removal → Diarization → Segmentation → ASR → Analysis
                ↓
            Transcripts + Confidence Scores
            Quality Metrics
```

## ASR Models Used

### Primary Model: OpenAI Whisper

We primarily used **OpenAI Whisper** as our ASR backbone due to its:

- **Robustness**: Excellent performance on diverse audio conditions
- **Multilingual Support**: Though we focus on English, the model handles accents well
- **Child Speech Compatibility**: Better performance on non-adult speech compared to many alternatives
- **Multiple Model Sizes**: From `tiny` to `large` for different speed/accuracy trade-offs

#### Whisper Model Variants

| Model | Parameters | Speed | Use Case |
|-------|------------|-------|----------|
| `tiny` | 39M | Fastest | Real-time applications |
| `base` | 74M | Fast | Quick processing |
| `small` | 244M | Medium | Balanced performance |
| `medium` | 769M | Slow | High accuracy needed |
| `large` | 1550M | Slowest | **Used in this project** - Best accuracy |
| `turbo` | 809M | Fast | optimized speed/accuracy |


## Experimental Approaches

AudioTechEquity implements three distinct experimental approaches to ASR for pediatric speech analysis, each designed to address different aspects of child speech recognition challenges:

### 1. Out-of-the-Box Approach
Utilizes pre-trained Whisper models without modification, leveraging the robust baseline performance of state-of-the-art ASR systems. This approach focuses on optimizing the audio processing pipeline (preprocessing, VAD, diarization, segmentation) to maximize the effectiveness of existing models on child speech data.

### 2. Fine-tuning Whisper Using Child Speech Data
Employs Parameter-Efficient Fine-Tuning (PEFT) with Low-Rank Adaptation (LoRA) to adapt pre-trained Whisper models specifically for pediatric speech characteristics. This approach targets the decoder attention layers to improve recognition of child-specific speech patterns while preserving the encoder's robust audio feature extraction capabilities.

### 3. Adversarial Debiasing
Implements adversarial training techniques to reduce demographic and linguistic biases in ASR performance, ensuring equitable recognition accuracy across different child populations, dialects, and speech development stages. This approach aims to create more inclusive and fair ASR systems for clinical applications.

## Out-of-the-Box Pipeline Approach

Before exploring fine-tuning options, AudioTechEquity provides a comprehensive out-of-the-box pipeline that uses pre-trained models. This approach follows a systematic workflow designed specifically for clinical speech assessment scenarios.

### Pipeline Architecture Overview

The out-of-the-box approach implements a multi-stage pipeline that processes raw audio through several specialized components before applying ASR. This methodology ensures optimal audio quality and speaker separation, which are critical for accurate speech recognition.

### Stage 1: Audio Preprocessing

The pipeline begins with comprehensive audio preprocessing to optimize the input for downstream components:

**Resampling**
- Target sample rate: 16 kHz (optimal for Whisper models)
- Maintains audio quality while reducing computational requirements
- Ensures consistency across different input sources

**Loudness Normalization**
- Target: <-20> dBFS for consistent volume levels
- Critical for pediatric speech which often has variable volume
- Prevents clipping and ensures optimal dynamic range

**Noise Reduction**
- Adaptive noise reduction with <80%> noise proportion threshold
- Stationary noise removal to enhance speech clarity
- Preserves speech characteristics while reducing background interference

### Stage 2: Voice Activity Detection (VAD)

VAD processing identifies speech segments and removes long silences:

**Silero VAD Implementation**
- Threshold: <0.5> for balanced sensitivity
- Minimum speech duration: <250ms> to capture short utterances
- Minimum silence duration: <100ms> to avoid over-segmentation
- Speech padding: 30ms to preserve speech boundaries

**Benefits for Pediatric Speech**
- Handles irregular speech patterns common in children
- Preserves short utterances and hesitations
- Reduces processing time by focusing on speech-containing segments

### Stage 3: Speaker Diarization

Critical for clinical scenarios involving multiple speakers (child, clinician, parent):

**NVIDIA TitaNet Model**
- State-of-the-art speaker verification model
- Optimized for clinical audio environments
- Handles overlapping speech and speaker changes

**Clustering Approach**
- Spectral clustering for robust speaker separation
- Automatic speaker count detection (up to 8 speakers)
- Enhanced count threshold: 0.8 for clinical accuracy

**Quality Assessment**
- Speaker count sanity checks to flag unusual results
- Duration analysis to identify potential over-segmentation
- Cosine distance evaluation for cluster quality

### Stage 4: Segmentation and Optimization

Prepares speaker-separated audio for optimal ASR processing:

**Child Speech Extraction**
- Extraction of child-only speech segments
- Padding application (200ms) to preserve speech boundaries

**Segment Processing**
- Individual segment analysis with noise filtering
- Optional smoothing techniques (crossfade) for seamless processing
- Quality checks for segment duration and content

**Chunking Strategy**
- Maximum segment length: 15 seconds (optimal for Whisper)
- VAD-based splitting for longer segments
- Maintains context while ensuring processing efficiency

### Stage 5: ASR Processing with OpenAI Whisper

The optimized audio segments are processed through Whisper models:

**Model Selection**
- Default: Whisper Large for balanced performance
- Configurable based on accuracy vs. speed requirements
- Automatic device selection (GPU/CPU) for optimal performance

**Processing Parameters**
- Temperature: 0.0 for deterministic results
- Beam size: 5 for balanced accuracy and speed
- Language: English (configurable for multilingual support)
- Timestamps: Enabled for detailed analysis

**Output Generation**
- Multiple format support (JSON, CSV, TXT, DOCX)
- Confidence scores for reliability assessment
- Segment-level and word-level timestamps

### Stage 6: Comprehensive Testing and Evaluation

Automated quality assessment ensures clinical reliability:

**Confidence Analysis**
- Average confidence per segment
- Low confidence segment identification
- Confidence distribution analysis

**Keyword Recognition Testing**
- GFTA word recognition accuracy
- Fuzzy matching for pronunciation variations
- Edit distance calculations for error analysis

**Quality Metrics**
- Word Error Rate (WER) when reference available
- Clinical relevance scoring
- Keyword recognition accuracy assessment

### Performance Characteristics

**Out-of-the-Box Results (Whisper Large)**
- Average Confidence: <> for clear pediatric speech
- Keyword Recognition: <>> exact match, <> fuzzy match


### When to Use Out-of-the-Box vs. Fine-Tuning

**Out-of-the-Box Approach is Optimal When:**
- Working with clear, well-recorded pediatric speech without any developmental disorders or heavy accents
- Need for immediate deployment without training data

**Consider Fine-Tuning When:**
- Specific dialect or accent adaptation needed
- Highly specialized vocabulary requirements or working with audio having developmental disorders
- Consistent recording environment and speaker characteristics
- Available training data for target population
- Maximum accuracy required for research applications

#### Custom Fine-tuned Models
- **Approach**: Fine-tuning Whisper on child speech datasets
- **Status**: Ongoing research

## Fine-tuning Process

### Overview

While the out-of-the-box pipeline provides baseline performance, fine-tuning Whisper models on pediatric speech data can yield significant improvements for specialized clinical applications. Our approach uses Parameter-Efficient Fine-Tuning (PEFT) with Low-Rank Adaptation (LoRA) to adapt pre-trained Whisper models to child speech characteristics.

### LoRA (Low-Rank Adaptation) Methodology

LoRA is a parameter-efficient fine-tuning technique that significantly reduces the computational requirements while maintaining model performance. Instead of fine-tuning all model parameters, LoRA introduces trainable low-rank matrices that adapt the model's behavior.

#### Technical Implementation

```python
from transformers import WhisperProcessor, WhisperForConditionalGeneration
from peft import LoraConfig, get_peft_model, TaskType

# Base model configuration
model_name_or_path = "openai/whisper-large-v2"
task = "transcribe"
language_abbr = "en"

# LoRA configuration - fine-tuning only the decoder part
config = LoraConfig(
    r=32,                    # Rank of adaptation
    lora_alpha=64,          # LoRA scaling parameter
    target_modules="^model\.decoder\..*(v_proj|q_proj)$",  # Target decoder attention layers
    lora_dropout=0.05,      # Dropout for LoRA layers
    bias="none",            # No bias adaptation
    task_type=TaskType.FEATURE_EXTRACTION
)

# Load base model
model = WhisperForConditionalGeneration.from_pretrained(
    model_name_or_path,
    torch_dtype=torch.float16,
    device_map="auto"
)

# Apply LoRA adaptation
model = get_peft_model(model, config)
```

#### Why LoRA for Whisper Fine-tuning?

**Computational Efficiency**
- Reduces trainable parameters from 1.55B to ~16M (1% of original)
- Enables fine-tuning on consumer GPUs with limited VRAM
- Faster training convergence compared to full fine-tuning

**Decoder-Only Adaptation**
- Targets only decoder attention layers (`v_proj` and `q_proj`)
- Preserves encoder's robust audio feature extraction
- Focuses adaptation on language modeling and transcription generation

**Parameter Selection Rationale**
- `r=32`: Sufficient rank for capturing child speech variations
- `lora_alpha=64`: 2x scaling for stronger adaptation signal
- `lora_dropout=0.05`: Light regularization to prevent overfitting

### PEFT (Parameter-Efficient Fine-Tuning) Framework

PEFT provides the infrastructure for efficient model adaptation:

```python
from peft import PeftModel, PeftConfig

# Training phase
model = get_peft_model(base_model, lora_config)
model.print_trainable_parameters()
# Output: trainable params: 15,728,640 || all params: 1,555,365,888 || trainable%: 1.01

# Inference phase - load adapted model
config = PeftConfig.from_pretrained("path/to/whisper-large-v2-child-speech-PEFT")
base_model = WhisperForConditionalGeneration.from_pretrained(config.base_model_name_or_path)
model = PeftModel.from_pretrained(base_model, "path/to/whisper-large-v2-child-speech-PEFT")
```

### Dataset Preparation

Our fine-tuning approach focuses on child speech characteristics:

**Data Sources**
<SEED>

### Training Strategy

**1. Base Model Selection**
- Start with `whisper-large-v2` for optimal baseline performance
- Pre-trained on diverse multilingual and multitask data
- Robust encoder features for audio processing

**2. Domain Adaptation Approach**
- Focus on decoder adaptation for language modeling
- Preserve encoder's audio feature extraction capabilities
- Target clinical vocabulary and child speech patterns

**3. Training Configuration**
```python
training_args = {
    "learning_rate": 1e-4,
    "batch_size": 16,
    "gradient_accumulation_steps": 2,
    "warmup_steps": 500,
    "max_steps": 5000,
    "eval_steps": 250,
    "save_steps": 500,
    "fp16": True,
    "dataloader_num_workers": 4
}
```

**4. Validation Strategy**
- Held-out child speech samples (20% of dataset)

### Fine-tuning Benefits
**Quantitative Improvements**
- **Word Error Rate (WER) Reduction**: <> improvement on child speech
- **Clinical Vocabulary Recognition**: <> improvement on <GFTA> words
- **Confidence Calibration**: Better uncertainty estimation for clinical decisions

**Qualitative Enhancements**
- **Articulation Error Sensitivity**: Enhanced detection of speech sound errors
- **Child-Specific Patterns**: Better handling of incomplete words and hesitations

### Performance Comparison

**Baseline vs Fine-tuned Results**

| Metric | Baseline Whisper-Large-v2 | LoRA Fine-tuned | Improvement |
|--------|---------------------------|-----------------|-------------|
| Child Speech WER | <> | <> | <> |
| GFTA Words WER | <> | <> | <> |
| Processing Speed | <>x realtime | <>x realtime | <> |
| Model Size | <> GB | <> GB + <> MB | <> |

### Technical Considerations

**Memory Requirements**
- Base model: <> VRAM for inference
- LoRA training: <> VRAM (vs <>GB for full fine-tuning)
- Adapter storage: <>MB (vs <>GB for full model)

**Training Time**
- LoRA fine-tuning: <> hours on <machine spec>
- Full fine-tuning equivalent: <> hours
- Convergence: Typically within <> steps

## Metadata and Output Format

### ASR Configuration

```yaml
asr:
  model_name: "large-v2"
  language: "en"
  fp16: true
  temperature: 0.0
  best_of: 5
  beam_size: 5
  return_timestamps: true
  return_confidence: true
```

### Output Formats

#### 1. JSON Format (Detailed)
```json
{
  "text": "Say duck. Duck. Good job.",
  "segments": [
    {
      "start": 0.0,
      "end": 1.2,
      "text": "Say duck.",
      "confidence": 0.95,
      "speaker": "ADULT"
    },
    {
      "start": 1.5,
      "end": 2.1,
      "text": "Duck.",
      "confidence": 0.87,
      "speaker": "CHILD"
    },
    {
      "start": 2.5,
      "end": 3.8,
      "text": "Good job.",
      "confidence": 0.92,
      "speaker": "ADULT"
    }
  ],
  "metadata": {
    "model": "whisper-large-v2",
    "language": "en",
    "duration": 3.8,
    "processing_time": 0.45
  }
}
```

#### 2. CSV Format (Tabular)
```csv
start_time,end_time,speaker,text,confidence,word_count
0.0,1.2,ADULT,"Say duck.",0.95,2
1.5,2.1,CHILD,"Duck.",0.87,1
2.5,3.8,ADULT,"Good job.",0.92,2
```

#### 3. DOCX Format (Clinical Reports)
```
[00:00] ADULT: Say duck.
[00:01] CHILD: Duck.
[00:02] ADULT: Good job.

--- Analysis ---
Target Word: "duck"
Child Production: "duck"
Accuracy: Correct
Confidence: 87%
```

### Confidence Scoring

Our confidence metrics include:
- **Segment-level confidence**: Overall reliability of each transcribed segment
- **Word-level confidence**: Individual word recognition certainty
- **Phoneme-level analysis**: Detailed articulation assessment (future feature)

## Evaluation Benchmarks

### Primary Metrics

#### Word Error Rate (WER)
```
WER = (S + D + I) / N
Where:
- S = Substitutions
- D = Deletions
- I = Insertions
- N = Total words in reference
```
### Domain-Specific Evaluation

#### GFTA Word Recognition Accuracy
```
Target Words: ["house", "door", "pig", "cup", "boy", "apple", ...]
Recognition Rate: 89.3%
Common Errors:
- "house" → "how" (articulation-related)
- "pig" → "big" (phoneme substitution)
```

#### Word Repetition Task Performance
```
Prompt-Response Pairs Analyzed: 1,247
Correct Repetitions Identified: 1,089 (87.3%)
False Positives: 23 (1.8%)
False Negatives: 135 (10.8%)
```

## Sample Transcripts

### Example 1: GFTA Assessment
```
Audio File: ATL009_task-gfta_acq-ZT.wav
Duration: 3:42

[00:15] CLINICIAN: Can you say "house"?
[00:17] CHILD: How.
[00:19] CLINICIAN: Try again. "House."
[00:21] CHILD: House.
[00:23] CLINICIAN: Good! Now say "door."
[00:25] CHILD: Door.
[00:27] CLINICIAN: Excellent. Say "pig."
[00:29] CHILD: Big.
[00:31] CLINICIAN: Let's try "pig" again.
[00:33] CHILD: Pig.

Analysis:
- Target words attempted: 4
- Correct on first attempt: 2/4 (50%)
- Correct after repetition: 4/4 (100%)
- Common error pattern: /p/ → /b/ substitution
```

### Example 2: Word Repetition Task
```
Audio File: CHI012_task-wordrep_acq-XT.wav
Duration: 2:18

[00:05] CLINICIAN: Say "cash."
[00:07] CHILD: Cash.
[00:09] CLINICIAN: Say "boat."
[00:11] CHILD: Boat.
[00:13] CLINICIAN: Say "duck."
[00:15] CHILD: Duck.
[00:17] CLINICIAN: Say "shoot."
[00:19] CHILD: Shoot.

Analysis:
- Repetition accuracy: 100%
- Average response latency: 1.2 seconds
- Confidence scores: 0.85-0.92
```

### Key Findings
<>


### Future Improvements

1. **Phoneme-level Analysis**: Detailed articulation error detection
2. **Real-time Feedback**: Live transcription during clinical sessions


## Usage Examples

### Command Line Interface
```bash
# Basic ASR processing
audtecheq asr --input segments/ --output transcriptions/

# High-accuracy processing
audtecheq asr --input segments/ --output transcriptions/ \
              --model large --with-timestamps

# Clinical report format
audtecheq asr --input segments/ --output transcriptions/ \
              --format docx --language en

# Batch processing with keyword analysis
audtecheq asr --input audio_directory/ --output transcriptions/ \
              --model turbo --format json \
              --with-confidence --extract-keywords
```

### Programmatic Usage
```python
from audtecheq.asr import ASRProcessor
from audtecheq.core.config import ASRConfig

# Configure ASR
config = ASRConfig(
    model_name="turbo",
    language="en",
    return_timestamps=True,
    return_confidence=True
)

# Process audio
asr = ASRProcessor(config=config)
result = asr.process("child_speech.wav", "transcription.json")

print(f"Transcription: {result['text']}")
print(f"Confidence: {result['confidence']:.2f}")

# Keyword analysis
keywords = asr.extract_keywords(result['text'], keyword_list=["duck", "house", "pig"])
print(f"Keywords found: {keywords}")
```

### Integration with Pipeline
```python
from audtecheq import PipelineConfig
from audtecheq.cli.commands import pipeline_command

# Complete pipeline with ASR
config = PipelineConfig.from_yaml("clinical_config.yaml")
args = argparse.Namespace(
    input="raw_audio.wav",
    output="results/",
    steps=["preprocess", "vad", "diarize", "segment", "asr"],
    oracle_speakers=2
)

pipeline_command(args, config)
```
