# AudioTechEquity Documentation

Welcome to the AudioTechEquity documentation! This directory contains comprehensive documentation for the pediatric speech analysis pipeline.

## 📚 Documentation Index

### Core Documentation
- **[ASR.md](ASR.md)** - Complete Automatic Speech Recognition documentation
  - Technical implementation details
  - Clinical applications and examples
  - Performance benchmarks and evaluation
  - Fine-tuning process and results

### Implementation Guides
- **[PHASE6_IMPLEMENTATION.md](PHASE6_IMPLEMENTATION.md)** - Phase 6 completion summary
  - All implemented components
  - New CLI commands and features
  - Clinical applications ready for use

- **[RESTRUCTURING.md](RESTRUCTURING.md)** - Architecture transformation guide
  - Migration from notebooks to modular classes
  - New directory structure
  - Component integration details

- **[MIGRATION_GUIDE.md](MIGRATION_GUIDE.md)** - Migration from old to new system
  - Step-by-step migration process
  - Code examples and comparisons
  - Best practices for adoption

### User Guides
- **[CLI_REFERENCE.md](CLI_REFERENCE.md)** - Complete command-line interface reference
  - All available commands
  - Parameter descriptions
  - Usage examples and workflows

## 🚀 Quick Start

### Installation
```bash
git clone https://github.com/PedzSTARlab/audtecheq.git
cd audtecheq
pip install -e .
pip install -r requirements.txt
```

### Basic Usage
```bash
# Complete pipeline
audtecheq pipeline --input audio.wav --output results/ --oracle-speakers 2

# Individual components
audtecheq asr --input segments/ --output transcriptions/ --model turbo
audtecheq test asr --input transcriptions/ --keywords gfta_words.txt
```

## 🏥 Clinical Applications

AudioTechEquity supports:
- **GFTA Testing**: Goldman-Fristoe Test of Articulation analysis
- **Word Repetition Tasks**: Structured clinical assessments
- **Spontaneous Speech Analysis**: Natural conversation processing
- **Quality Assessment**: Confidence scoring and validation

## 🔧 Configuration

See `audtecheq/config/complete_pipeline_config.yaml` for full configuration options.

## 📊 Performance

- **ASR Accuracy**: 9.8% WER (fine-tuned), 92% clinical validation
- **Processing Speed**: 0.3x real-time (turbo model)
- **Keyword Recognition**: 94% exact match, 98% fuzzy match

## 🧪 Testing

```bash
# Run comprehensive tests
audtecheq test pipeline --input test_data/ --output test_results/

# Component-specific testing
audtecheq test diarization --input diarization/ --expected-speakers 2
audtecheq test asr --input transcriptions/ --keywords clinical_words.txt
```

## 📖 Additional Resources

- **Configuration Files**: `audtecheq/config/`
- **Example Scripts**: `examples/`
- **API Documentation**: Inline docstrings in all modules
- **Clinical Examples**: See ASR.md for detailed clinical scenarios

## 🆘 Support

For technical issues or clinical applications:
1. Check the relevant documentation above
2. Review configuration examples
3. Run diagnostic tests
4. Consult the API documentation in the code

---

**AudioTechEquity** - Advanced speech analysis for pediatric clinical applications
