# Enhanced AudioTechEquity Features

This document describes the new features implemented according to the pipeline flow diagram, completing the remaining components for a comprehensive audio processing pipeline.

## Overview

The enhanced AudioTechEquity pipeline now includes all components from the flow diagram:

```
Raw Audio → Preprocessing → VAD → Discard Long Silences → Diarization → Testing
```

## New Components

### 1. Enhanced Silence Processing (`SilenceProcessor`)

**Location**: `audtecheq/vad/silence_processor.py`

**Purpose**: Implements the "Discard long silences" step from the flow diagram.

**Features**:
- Configurable maximum silence duration threshold
- VAD-aware silence detection
- Boundary preservation options
- Energy-based fallback detection

**Usage**:
```python
from audtecheq.vad import SilenceProcessor

processor = SilenceProcessor()
result = processor.process(
    "input.wav",
    "output.wav",
    max_silence_duration=2.0,
    preserve_boundaries=True
)
```

**CLI Usage**:
```bash
audtecheq discard-silences --input audio.wav --output processed.wav --max-silence-duration 2.0
```

### 2. Enhanced Diarization Testing (`EnhancedDiarizationTester`)

**Location**: `audtecheq/testing/enhanced_diarization_tester.py`

**Purpose**: Implements the detailed testing metrics from the flow diagram.

**Features**:

#### Speaker Count Sanity Checks
- Flags files with excessive speaker counts (> n threshold)
- Validates against expected speaker counts
- Provides statistical analysis of speaker distributions

#### Cluster Evaluation (Cosine Distance)
- Analyzes intra-cluster vs inter-cluster distances
- Calculates separation scores
- Evaluates cluster quality metrics
- Requires speaker embeddings for full analysis

**Usage**:
```python
from audtecheq.testing import EnhancedDiarizationTester

tester = EnhancedDiarizationTester()
result = tester.process(
    "diarization_results/",
    "test_report.json",
    embeddings_path="speaker_embeddings.json",
    expected_speakers=2,
    max_speakers_threshold=6
)
```

**CLI Usage**:
```bash
audtecheq test enhanced-diarization \
    --input diarization_results/ \
    --embeddings speaker_embeddings.json \
    --expected-speakers 2 \
    --max-speakers-threshold 6 \
    --output detailed_report.json
```

## Enhanced Pipeline Flow

The complete pipeline now follows the exact flow from the diagram:

### 1. Preprocessing
- **Resample**: Convert to target sample rate
- **Normalize loudness**: Adjust to target dBFS
- **Noise reduction**: Apply spectral noise reduction

### 2. Voice Activity Detection (VAD)
- Detect speech segments
- Generate timestamps
- Remove basic silences

### 3. Discard Long Silences (NEW)
- Remove silences exceeding threshold
- Preserve speech boundaries
- Use VAD timestamps for accuracy

### 4. Diarization
- **Use spectral clustering**: As specified in diagram
- Identify and segment speakers
- Generate RTTM and CSV outputs

### 5. Testing - Diarization (NEW)
- **Speaker count sanity checks**: Flag if #speakers > n
- **Evaluate clusters**: Analyze cosine distances (intra vs inter-cluster)

## CLI Enhancements

### New Commands

1. **Silence Processing**:
   ```bash
   audtecheq discard-silences --input audio.wav --output processed.wav
   ```

2. **Enhanced Testing**:
   ```bash
   audtecheq test enhanced-diarization --input results/ --output report.json
   ```

### Enhanced Pipeline Command

The pipeline command now includes the silence processing step:

```bash
audtecheq pipeline \
    --input audio.wav \
    --output results/ \
    --steps preprocess vad discard-silences diarize test
```

## Configuration

### Silence Processing Configuration
```python
silence_config = {
    'max_silence_duration_sec': 2.0,
    'min_speech_gap_sec': 0.1,
    'silence_threshold': 0.01,
    'preserve_boundaries': True
}
```

### Enhanced Testing Configuration
```python
test_config = {
    'max_speakers_threshold': 6,
    'cosine_distance_threshold': 0.3
}
```

## Output Formats

### Enhanced Test Report Structure
```json
{
  "tests": {
    "speaker_count_sanity": {
      "flags": [...],
      "statistics": {...}
    },

    "cluster_evaluation": {
      "flags": [...],
      "statistics": {...}
    }
  },
  "flags": {
    "speaker_count_flags": [...],
    "cluster_flags": [...]
  },
  "summary": {
    "total_flags_raised": 5,
    "overall_quality_assessment": "good",
    "insights": [...]
  }
}
```

### Flag Types

**Speaker Count Flags**:
- `excessive_speakers`: Too many speakers detected
- `speaker_count_mismatch`: Doesn't match expected count

**Cluster Flags**:
- `poor_cluster_separation`: Low separation score
- `high_intra_cluster_distance`: Poor within-cluster cohesion
- `low_inter_cluster_distance`: Poor between-cluster separation

## Examples

### Complete Enhanced Pipeline
```python
from audtecheq import (
    AudioPreprocessor, VADProcessor, SilenceProcessor,
    SpeakerDiarizer, EnhancedDiarizationTester
)

# Run complete pipeline
preprocessor = AudioPreprocessor()
vad = VADProcessor()
silence_processor = SilenceProcessor()
diarizer = SpeakerDiarizer()
tester = EnhancedDiarizationTester()

# Process step by step
preprocess_result = preprocessor.process("input.wav", "preprocessed.wav")
vad_result = vad.process("preprocessed.wav", "vad_processed.wav")
silence_result = silence_processor.process("vad_processed.wav", "silence_processed.wav")
diarization_result = diarizer.process("silence_processed.wav", "diarization/")
test_result = tester.process("diarization/", "test_report.json")
```

### CLI Pipeline
```bash
# Complete pipeline with all new features
audtecheq pipeline \
    --input recording.wav \
    --output results/ \
    --steps preprocess vad discard-silences diarize test \
    --oracle-speakers 2

# Individual components
audtecheq discard-silences --input audio.wav --output clean.wav
audtecheq test enhanced-diarization --input diarization/ --expected-speakers 2
```

## Integration with Existing Code

The new components are fully integrated with the existing AudioTechEquity framework:

- **Modular Design**: Each component can be used independently
- **CLI Integration**: All components available through CLI
- **Configuration System**: Uses existing config framework
- **Error Handling**: Consistent error handling and logging
- **Testing Framework**: Comprehensive test coverage

## Performance Considerations

- **Silence Processing**: Minimal overhead, processes in chunks
- **Enhanced Testing**: CPU-intensive for cluster analysis with embeddings
- **Memory Usage**: Optimized for large audio files
- **Batch Processing**: Supports directory-level processing

This completes the implementation of all remaining components from the flow diagram, providing a comprehensive audio processing pipeline with detailed quality assessment capabilities.
