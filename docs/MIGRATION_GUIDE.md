# Migration Guide: From Notebooks to Modular Architecture

This guide helps users migrate from the old Jupyter notebook-based workflow to the new modular, class-based AudioTechEquity architecture.

## Table of Contents

1. [Overview of Changes](#overview-of-changes)
2. [Quick Migration](#quick-migration)
3. [Detailed Component Migration](#detailed-component-migration)
4. [Configuration Migration](#configuration-migration)
5. [CLI Usage](#cli-usage)
6. [Troubleshooting](#troubleshooting)

## Overview of Changes

### Before: Notebook-Based Workflow
```
1. Open vanilla.ipynb
2. Manually edit file paths in cells
3. Run cells sequentially
4. Copy/paste code for different files
5. Manual parameter tuning in code
```

### After: Modular Architecture
```
1. Install audtecheq package
2. Use CLI commands or Python API
3. Configure via YAML files
4. Batch processing support
5. Consistent interfaces across components
```

## Quick Migration

### For CLI Users

**Old Workflow (Notebook)**:
```python
# Cell 1: Set paths
raw_folder = "/path/to/audio"
output_folder = "/path/to/output"

# Cell 2-10: Run preprocessing, VAD, diarization manually
```

**New Workflow (CLI)**:
```bash
# Single command for complete pipeline
audtecheq pipeline --input /path/to/audio --output /path/to/output

# Or step by step
audtecheq preprocess --input /path/to/audio --output /path/to/preprocessed
audtecheq vad --input /path/to/preprocessed --output /path/to/vad
audtecheq diarize --input /path/to/vad --output /path/to/diarization
```

### For Python Users

**Old Workflow (Notebook)**:
```python
# Copy/paste code from notebook cells
from pydub import AudioSegment
import noisereduce as nr

# Manual processing
audio = AudioSegment.from_file(audio_path)
resampled_audio = audio.set_frame_rate(16000).set_channels(1)
# ... more manual steps
```

**New Workflow (Python API)**:
```python
from audtecheq import AudioPreprocessor, SpeakerDiarizer

# Automated processing with error handling
preprocessor = AudioPreprocessor()
result = preprocessor.process("input.wav", "output.wav")

diarizer = SpeakerDiarizer()
diar_result = diarizer.process("audio.wav", "diarization/")
```

## Detailed Component Migration

### 1. Audio Preprocessing

**Old Notebook Code** (`vanilla.ipynb` cells 3-10):
```python
# Resampling
from pydub import AudioSegment
audio = AudioSegment.from_file(audio_path)
resampled_audio = audio.set_frame_rate(16000).set_channels(1)
resampled_audio.export(output_path, format="wav")

# Normalization
change_in_dBFS = target_dBFS - audio.dBFS
normalized_audio = audio.apply_gain(change_in_dBFS)
normalized_audio.export(output_path, format="wav")

# Denoising
import noisereduce as nr
import soundfile as sf
audio, sr = sf.read(input_path)
reduced_noise = nr.reduce_noise(y=audio, sr=sr, prop_decrease=0.8)
sf.write(output_path, reduced_noise, sr)
```

**New Modular Approach**:
```python
from audtecheq.preprocessing import AudioPreprocessor
from audtecheq.core.config import PreprocessingConfig

# Option 1: Use default settings
preprocessor = AudioPreprocessor()
result = preprocessor.process("input.wav", "output.wav")

# Option 2: Custom configuration
config = PreprocessingConfig(
    target_sample_rate=16000,
    target_channels=1,
    target_dbfs=-20.0,
    noise_reduction_prop=0.8
)
preprocessor = AudioPreprocessor(config=config)
result = preprocessor.process("input.wav", "output.wav")

# Option 3: Step-by-step with intermediate files
result = preprocessor.process_step_by_step(
    "input.wav", 
    "output_dir/", 
    keep_intermediates=True
)
```

**CLI Equivalent**:
```bash
# Basic preprocessing
audtecheq preprocess --input input.wav --output output.wav

# With custom parameters
audtecheq preprocess --input input.wav --output output.wav \
                    --sample-rate 16000 \
                    --target-dbfs -20.0 \
                    --noise-reduction 0.8 \
                    --keep-intermediates
```

### 2. Voice Activity Detection

**Old Notebook Code** (`vanilla.ipynb` cells 11-15):
```python
import nemo.collections.asr as nemo_asr
# Manual VAD setup and processing
# Complex configuration and model loading
```

**New Modular Approach**:
```python
from audtecheq.vad import VADProcessor
from audtecheq.core.config import VADConfig

# Option 1: Default VAD
vad = VADProcessor()
result = vad.process("input.wav", "output.wav")

# Option 2: Custom configuration
config = VADConfig(
    threshold=0.5,
    min_speech_duration_ms=250,
    min_silence_duration_ms=100
)
vad = VADProcessor(config=config)
result = vad.process("input.wav", "output.wav")

# Analyze speech activity without processing
analysis = vad.analyze_speech_activity("input.wav")
print(f"Speech ratio: {analysis['speech_ratio']:.2f}")
```

**CLI Equivalent**:
```bash
audtecheq vad --input input.wav --output output.wav \
              --threshold 0.5 \
              --min-speech-duration 250
```

### 3. Speaker Diarization

**Old Notebook Code** (`vanilla.ipynb` cells 16-25):
```python
from nemo.collections.asr.models import ClusteringDiarizer
# Manual configuration setup
# Complex RTTM file handling
```

**New Modular Approach**:
```python
from audtecheq.diarization import SpeakerDiarizer
from audtecheq.core.config import DiarizationConfig

# Option 1: Automatic speaker detection
diarizer = SpeakerDiarizer()
result = diarizer.process("input.wav", "output_dir/")

# Option 2: With known speaker count
config = DiarizationConfig(oracle_num_speakers=2)
diarizer = SpeakerDiarizer(config=config)
result = diarizer.process("input.wav", "output_dir/")

# Quality analysis
quality = diarizer.analyze_diarization_quality(result['segments'])
print(f"Quality score: {quality['quality_score']:.2f}")
print(f"Issues: {quality['issues']}")
```

**CLI Equivalent**:
```bash
# Automatic speaker detection
audtecheq diarize --input input.wav --output diarization_results/

# With known speaker count
audtecheq diarize --input input.wav --output diarization_results/ \
                  --oracle-speakers 2 \
                  --max-speakers 4
```

## Configuration Migration

### Old Approach: Hardcoded Parameters
```python
# Scattered throughout notebook cells
target_sample_rate = 16000
target_channels = 1
target_dBFS = -20.0
prop_decrease = 0.8
oracle_num_speakers = None
max_num_speakers = 8
```

### New Approach: Centralized Configuration

**YAML Configuration File** (`config.yaml`):
```yaml
# Global settings
device: "auto"
log_level: "INFO"

# Preprocessing
preprocessing:
  target_sample_rate: 16000
  target_channels: 1
  target_dbfs: -20.0
  noise_reduction_prop: 0.8

# VAD
vad:
  threshold: 0.5
  min_speech_duration_ms: 250

# Diarization
diarization:
  oracle_num_speakers: null
  max_num_speakers: 8
  clustering_backend: "spectral"
```

**Using Configuration**:
```python
from audtecheq import PipelineConfig

# Load from file
config = PipelineConfig.from_yaml("config.yaml")

# Use with processors
preprocessor = AudioPreprocessor(config=config.preprocessing)
diarizer = SpeakerDiarizer(config=config.diarization)
```

**CLI with Configuration**:
```bash
audtecheq pipeline --config config.yaml --input audio.wav --output results/
```

## CLI Usage

### Complete Pipeline
```bash
# Process single file
audtecheq pipeline --input audio.wav --output results/

# Process directory
audtecheq pipeline --input audio_directory/ --output results_directory/

# With configuration file
audtecheq pipeline --config my_config.yaml --input audio.wav --output results/

# Specific steps only
audtecheq pipeline --input audio.wav --output results/ \
                   --steps preprocess vad diarize

# Keep intermediate files
audtecheq pipeline --input audio.wav --output results/ \
                   --keep-intermediates
```

### Individual Components
```bash
# Preprocessing
audtecheq preprocess --input audio.wav --output preprocessed.wav \
                    --sample-rate 16000 \
                    --target-dbfs -20.0

# VAD
audtecheq vad --input preprocessed.wav --output vad_output.wav \
              --threshold 0.5

# Diarization
audtecheq diarize --input vad_output.wav --output diarization/ \
                  --oracle-speakers 2
```

### Testing and Evaluation
```bash
# Test diarization quality
audtecheq test diarization --input diarization_results/ \
                          --expected-speakers 2

# Test ASR quality
audtecheq test asr --input transcriptions/ \
                   --keywords gfta_words.txt
```

## Batch Processing

### Old Approach: Manual Loops
```python
# Manual iteration through files
for filename in os.listdir(input_folder):
    if filename.endswith(".wav"):
        # Process each file manually
        # Copy/paste processing code
```

### New Approach: Built-in Batch Processing

**Python API**:
```python
from audtecheq import AudioPreprocessor

preprocessor = AudioPreprocessor()
results = preprocessor.process_batch("input_directory/", "output_directory/")

print(f"Processed: {len(results['processed_files'])}")
print(f"Errors: {len(results['errors'])}")
```

**CLI**:
```bash
# Automatically processes all audio files in directory
audtecheq pipeline --input audio_directory/ --output results_directory/
```

## Error Handling

### Old Approach: Manual Try/Catch
```python
try:
    # Processing code
    pass
except Exception as e:
    print(f"Error: {e}")
    # Manual error handling
```

### New Approach: Built-in Error Handling
```python
from audtecheq.core.exceptions import ProcessingError, ValidationError

try:
    result = preprocessor.process("input.wav", "output.wav")
except ValidationError as e:
    print(f"Input validation failed: {e}")
except ProcessingError as e:
    print(f"Processing failed: {e}")
    # Detailed error information available
```

## Troubleshooting

### Common Migration Issues

**1. Import Errors**
```python
# Old (doesn't work)
from audtecheq import some_old_function

# New (correct)
from audtecheq import AudioPreprocessor, SpeakerDiarizer
from audtecheq.core.config import PipelineConfig
```

**2. Path Handling**
```python
# Old (manual path handling)
output_path = os.path.join(output_folder, filename)

# New (automatic path handling)
result = preprocessor.process("input.wav", "output.wav")
# Paths are automatically validated and created
```

**3. Configuration Issues**
```python
# Old (scattered parameters)
target_sample_rate = 16000
# ... many parameters in different places

# New (centralized configuration)
config = PipelineConfig.from_yaml("config.yaml")
# All parameters in one place
```

**4. Dependency Issues**
```bash
# If you get import errors, install missing dependencies
pip install -r requirements.txt

# For NeMo (optional but recommended)
pip install nemo_toolkit[asr]
```

### Getting Help

1. **Check the logs**: The new system provides detailed logging
2. **Use CLI help**: `audtecheq --help` or `audtecheq <command> --help`
3. **Check configuration**: Validate your YAML configuration files
4. **Review examples**: See `examples/example_usage.py` for working code

### Performance Considerations

**Old Approach**: Manual optimization required
**New Approach**: Built-in optimizations
- Automatic device detection (CPU/GPU)
- Efficient batch processing
- Memory management
- Progress tracking

```bash
# Force CPU usage if needed
audtecheq pipeline --device cpu --input audio.wav --output results/

# Use GPU if available
audtecheq pipeline --device cuda --input audio.wav --output results/
```

This migration guide should help you transition from the notebook-based workflow to the new modular architecture. The new system provides better error handling, configuration management, and scalability while maintaining the same core functionality.
