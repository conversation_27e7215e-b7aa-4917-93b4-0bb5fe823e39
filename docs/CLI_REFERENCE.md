# AudioTechEquity CLI Reference

Quick reference guide for all AudioTechEquity command-line interface commands.

## Table of Contents

1. [Global Options](#global-options)
2. [Pipeline Command](#pipeline-command)
3. [Individual Components](#individual-components)
4. [Testing Commands](#testing-commands)
5. [Configuration](#configuration)
6. [Examples](#examples)

## Global Options

Available for all commands:

```bash
audtecheq [GLOBAL_OPTIONS] COMMAND [COMMAND_OPTIONS]
```

**Global Options:**
- `--config, -c PATH`: Path to configuration YAML file
- `--log-level LEVEL`: Logging level (DEBUG, INFO, WARNING, ERROR)
- `--device DEVICE`: Device to use (auto, cpu, cuda)
- `--version, -v`: Show version information
- `--help, -h`: Show help message

## Pipeline Command

Run the complete end-to-end pipeline:

```bash
audtecheq pipeline [OPTIONS]
```

**Required Arguments:**
- `--input, -i PATH`: Input audio file or directory
- `--output, -o PATH`: Output directory for all results

**Optional Arguments:**
- `--steps STEPS`: Pipeline steps to run (default: preprocess vad diarize segment asr)
  - Choices: `preprocess`, `vad`, `diarize`, `segment`, `asr`, `test`
- `--oracle-speakers N`: Known number of speakers (if available)
- `--keep-intermediates`: Keep intermediate files from each step

**Examples:**
```bash
# Complete pipeline
audtecheq pipeline --input audio.wav --output results/

# Specific steps only
audtecheq pipeline --input audio.wav --output results/ \
                   --steps preprocess vad diarize

# With known speaker count
audtecheq pipeline --input audio.wav --output results/ \
                   --oracle-speakers 2 \
                   --keep-intermediates

# Batch processing
audtecheq pipeline --input audio_directory/ --output results_directory/
```

## Individual Components

### Preprocessing

Preprocess audio files (resample, normalize, denoise):

```bash
audtecheq preprocess [OPTIONS]
```

**Required Arguments:**
- `--input, -i PATH`: Input audio file or directory
- `--output, -o PATH`: Output file or directory

**Optional Arguments:**
- `--sample-rate RATE`: Target sample rate in Hz (default: 16000)
- `--channels N`: Target number of channels (default: 1)
  - Choices: 1 (mono), 2 (stereo)
- `--target-dbfs LEVEL`: Target loudness in dBFS (default: -20.0)
- `--noise-reduction STRENGTH`: Noise reduction strength 0.0-1.0 (default: 0.8)
- `--keep-intermediates`: Keep intermediate processing files

**Examples:**
```bash
# Basic preprocessing
audtecheq preprocess --input audio.wav --output preprocessed.wav

# Custom settings
audtecheq preprocess --input audio.wav --output preprocessed.wav \
                    --sample-rate 22050 \
                    --target-dbfs -18.0 \
                    --noise-reduction 0.9

# Batch processing with intermediates
audtecheq preprocess --input audio_dir/ --output processed_dir/ \
                    --keep-intermediates
```

### Voice Activity Detection

Apply Voice Activity Detection to remove long silences:

```bash
audtecheq vad [OPTIONS]
```

**Required Arguments:**
- `--input, -i PATH`: Input audio file or directory
- `--output, -o PATH`: Output file or directory

**Optional Arguments:**
- `--threshold FLOAT`: VAD threshold (default: 0.5)
- `--min-speech-duration MS`: Minimum speech duration in ms (default: 250)
- `--min-silence-duration MS`: Minimum silence duration in ms (default: 100)

**Examples:**
```bash
# Basic VAD
audtecheq vad --input preprocessed.wav --output vad_output.wav

# Sensitive VAD (lower threshold)
audtecheq vad --input preprocessed.wav --output vad_output.wav \
              --threshold 0.3 \
              --min-speech-duration 200

# Conservative VAD (higher threshold)
audtecheq vad --input preprocessed.wav --output vad_output.wav \
              --threshold 0.7 \
              --min-silence-duration 500
```

### Speaker Diarization

Perform speaker diarization to identify and segment speakers:

```bash
audtecheq diarize [OPTIONS]
```

**Required Arguments:**
- `--input, -i PATH`: Input audio file or directory
- `--output, -o PATH`: Output directory for diarization results

**Optional Arguments:**
- `--oracle-speakers N`: Known number of speakers (if available)
- `--max-speakers N`: Maximum number of speakers (default: 8)
- `--clustering-backend ALGORITHM`: Clustering algorithm (default: spectral)
  - Choices: `spectral`, `kmeans`, `ahc`

**Examples:**
```bash
# Automatic speaker detection
audtecheq diarize --input vad_output.wav --output diarization_results/

# With known speaker count
audtecheq diarize --input vad_output.wav --output diarization_results/ \
                  --oracle-speakers 2

# Custom clustering
audtecheq diarize --input vad_output.wav --output diarization_results/ \
                  --max-speakers 4 \
                  --clustering-backend kmeans
```

### Segmentation

Extract and process speaker segments:

```bash
audtecheq segment [OPTIONS]
```

**Required Arguments:**
- `--input, -i PATH`: Input audio file or directory
- `--diarization, -d PATH`: Diarization results directory or CSV file
- `--output, -o PATH`: Output directory for segments

**Optional Arguments:**
- `--padding SECONDS`: Padding duration in seconds (default: 0.2)
- `--max-segment-length SECONDS`: Maximum segment length in seconds (default: 15.0)
- `--crossfade`: Apply crossfade to segments
- `--extract-speaker SPEAKER`: Extract only specific speaker (most active if not specified)

**Examples:**
```bash
# Basic segmentation
audtecheq segment --input audio.wav \
                  --diarization diarization_results/ \
                  --output segments/

# With crossfade and custom padding
audtecheq segment --input audio.wav \
                  --diarization diarization_results/audio.csv \
                  --output segments/ \
                  --padding 0.5 \
                  --crossfade

# Extract specific speaker
audtecheq segment --input audio.wav \
                  --diarization diarization_results/ \
                  --output segments/ \
                  --extract-speaker SPEAKER_01
```

### Automatic Speech Recognition

Perform speech recognition on audio segments:

```bash
audtecheq asr [OPTIONS]
```

**Required Arguments:**
- `--input, -i PATH`: Input audio file, directory, or segments
- `--output, -o PATH`: Output directory for transcriptions

**Optional Arguments:**
- `--model MODEL`: Whisper model to use (default: turbo)
  - Choices: `tiny`, `base`, `small`, `medium`, `large`, `turbo`
- `--language LANG`: Language code (default: en)
- `--format FORMAT`: Output format (default: txt)
  - Choices: `txt`, `json`, `csv`, `docx`
- `--with-timestamps`: Include timestamps in output

**Examples:**
```bash
# Basic ASR
audtecheq asr --input segments/ --output transcriptions/

# With specific model and format
audtecheq asr --input segments/ --output transcriptions/ \
              --model large \
              --format json \
              --with-timestamps

# Different language
audtecheq asr --input segments/ --output transcriptions/ \
              --language es \
              --format docx
```

## Testing Commands

Test and evaluate pipeline components:

```bash
audtecheq test COMPONENT [OPTIONS]
```

### Diarization Testing

Test diarization quality:

```bash
audtecheq test diarization [OPTIONS]
```

**Required Arguments:**
- `--input, -i PATH`: Diarization results directory

**Optional Arguments:**
- `--expected-speakers N`: Expected number of speakers
- `--output, -o PATH`: Output report file

**Examples:**
```bash
# Basic diarization testing
audtecheq test diarization --input diarization_results/

# With expected speaker count
audtecheq test diarization --input diarization_results/ \
                          --expected-speakers 2 \
                          --output quality_report.json
```

### ASR Testing

Test ASR quality and keyword recognition:

```bash
audtecheq test asr [OPTIONS]
```

**Required Arguments:**
- `--input, -i PATH`: Transcription files or directory

**Optional Arguments:**
- `--keywords PATH`: Keywords file for testing
- `--output, -o PATH`: Output report file

**Examples:**
```bash
# Basic ASR testing
audtecheq test asr --input transcriptions/

# With keyword testing
audtecheq test asr --input transcriptions/ \
                   --keywords gfta_words.txt \
                   --output asr_report.json
```

## Configuration

### Using Configuration Files

```bash
# Use custom configuration
audtecheq pipeline --config my_config.yaml --input audio.wav --output results/

# Any command can use configuration
audtecheq preprocess --config my_config.yaml --input audio.wav --output preprocessed.wav
```

### Configuration File Format

Create a `config.yaml` file:

```yaml
# Global settings
device: "auto"  # auto, cpu, cuda
log_level: "INFO"

# Component-specific settings
preprocessing:
  target_sample_rate: 16000
  target_channels: 1
  target_dbfs: -20.0
  noise_reduction_prop: 0.8

vad:
  threshold: 0.5
  min_speech_duration_ms: 250
  min_silence_duration_ms: 100

diarization:
  oracle_num_speakers: null
  max_num_speakers: 8
  clustering_backend: "spectral"

asr:
  model_name: "turbo"
  language: "en"
  fp16: true
```

## Examples

### Complete Workflows

**Basic Pipeline:**
```bash
audtecheq pipeline --input audio.wav --output results/
```

**Research Workflow:**
```bash
# Step 1: Preprocess with high quality settings
audtecheq preprocess --input raw_audio.wav --output preprocessed.wav \
                    --sample-rate 22050 --noise-reduction 0.9

# Step 2: Conservative VAD
audtecheq vad --input preprocessed.wav --output vad_output.wav \
              --threshold 0.7

# Step 3: Diarization with known speakers
audtecheq diarize --input vad_output.wav --output diarization/ \
                  --oracle-speakers 2

# Step 4: Extract segments with crossfade
audtecheq segment --input vad_output.wav \
                  --diarization diarization/ \
                  --output segments/ \
                  --crossfade

# Step 5: High-quality ASR
audtecheq asr --input segments/ --output transcriptions/ \
              --model large --format json --with-timestamps

# Step 6: Quality assessment
audtecheq test diarization --input diarization/ --expected-speakers 2
audtecheq test asr --input transcriptions/ --keywords keywords.txt
```

**Batch Processing:**
```bash
# Process multiple files
audtecheq pipeline --input audio_directory/ --output results_directory/ \
                   --keep-intermediates

# Custom configuration for batch
audtecheq pipeline --config batch_config.yaml \
                   --input large_dataset/ \
                   --output batch_results/
```

### Device-Specific Usage

**CPU Only:**
```bash
audtecheq pipeline --device cpu --input audio.wav --output results/
```

**GPU Acceleration:**
```bash
audtecheq pipeline --device cuda --input audio.wav --output results/
```

**Auto Detection:**
```bash
audtecheq pipeline --device auto --input audio.wav --output results/
```

### Debugging and Logging

**Verbose Logging:**
```bash
audtecheq --log-level DEBUG pipeline --input audio.wav --output results/
```

**Quiet Mode:**
```bash
audtecheq --log-level ERROR pipeline --input audio.wav --output results/
```

### Help and Information

**General Help:**
```bash
audtecheq --help
```

**Command-Specific Help:**
```bash
audtecheq pipeline --help
audtecheq preprocess --help
audtecheq diarize --help
```

**Version Information:**
```bash
audtecheq --version
```

This CLI reference provides comprehensive coverage of all available commands and options in the AudioTechEquity pipeline.
