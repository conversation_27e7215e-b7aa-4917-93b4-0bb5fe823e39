#!/usr/bin/env python3
"""
Comprehensive Data Pre-processing Script for AudioTechEquity

This script performs complete preprocessing on all audio files in the data directory,
organizing outputs in a derivative/pre-processed structure and generating detailed reports.
"""

import argparse
import json
import logging
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Any

# Add current directory to path for imports
sys.path.insert(0, '.')

from audtecheq.core.data_manager import DataManager
from audtecheq.preprocessing import AudioPreprocessor
from audtecheq.core.config import PreprocessingConfig


def setup_logging(log_level: str = "INFO", log_file: str = None) -> logging.Logger:
    """Setup logging configuration."""
    logger = logging.getLogger(__name__)
    logger.setLevel(getattr(logging, log_level.upper()))

    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # File handler if specified
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    return logger


def create_derivative_structure(data_dir: Path, derivative_dir: Path) -> Dict[str, Path]:
    """
    Create organized derivative directory structure.

    Parameters
    ----------
    data_dir : Path
        Source data directory
    derivative_dir : Path
        Derivative directory to create

    Returns
    -------
    Dict[str, Path]
        Mapping of processing steps to output directories
    """
    # Create main derivative directory
    derivative_dir.mkdir(parents=True, exist_ok=True)

    # Create subdirectories for each processing step
    processing_steps = {
        'resampled': derivative_dir / 'resampled',
        'normalized': derivative_dir / 'normalized',
        'denoised': derivative_dir / 'denoised',
        'preprocessed': derivative_dir / 'preprocessed',
        'reports': derivative_dir / 'reports'
    }

    for step_dir in processing_steps.values():
        step_dir.mkdir(parents=True, exist_ok=True)

    return processing_steps


def find_audio_files(data_dir: Path) -> List[Path]:
    """Find all audio files in the data directory."""
    audio_extensions = ['.wav', '.mp3', '.flac', '.m4a']
    audio_files = []

    for ext in audio_extensions:
        audio_files.extend(data_dir.rglob(f'*{ext}'))

    return sorted(audio_files)


def run_cli_command(command: List[str], logger: logging.Logger) -> Tuple[bool, str, str]:
    """
    Run a CLI command and capture output.

    Parameters
    ----------
    command : List[str]
        Command to run
    logger : logging.Logger
        Logger instance

    Returns
    -------
    Tuple[bool, str, str]
        Success status, stdout, stderr
    """
    logger.info(f"Running command: {' '.join(command)}")

    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout per file
        )

        success = result.returncode == 0
        if not success:
            logger.error(f"Command failed with return code {result.returncode}")
            logger.error(f"STDERR: {result.stderr}")

        return success, result.stdout, result.stderr

    except subprocess.TimeoutExpired:
        logger.error(f"Command timed out after 5 minutes")
        return False, "", "Command timed out"
    except Exception as e:
        logger.error(f"Failed to run command: {e}")
        return False, "", str(e)


def preprocess_file_direct(
    input_file: Path,
    output_file: Path,
    config: Dict[str, Any],
    logger: logging.Logger
) -> Dict[str, Any]:
    """
    Preprocess a single file using AudioPreprocessor directly.

    Parameters
    ----------
    input_file : Path
        Input audio file
    output_file : Path
        Output file path
    config : Dict[str, Any]
        Preprocessing configuration
    logger : logging.Logger
        Logger instance

    Returns
    -------
    Dict[str, Any]
        Processing result
    """
    # Ensure output directory exists
    output_file.parent.mkdir(parents=True, exist_ok=True)

    start_time = datetime.now()

    try:
        # Create preprocessing configuration
        preprocess_config = PreprocessingConfig(
            target_sample_rate=config.get('sample_rate', 16000),
            target_channels=config.get('channels', 1),
            target_dbfs=config.get('target_dbfs', -20.0),
            noise_reduction_prop=config.get('noise_reduction', 0.8)
        )

        # Initialize preprocessor
        preprocessor = AudioPreprocessor(config=preprocess_config)

        # Process the file
        if config.get('keep_intermediates', False):
            result = preprocessor.process_step_by_step(
                input_file,
                output_file.parent,
                keep_intermediates=True
            )
        else:
            result = preprocessor.process(input_file, output_file)

        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()

        return {
            'input_file': str(input_file),
            'output_file': str(output_file),
            'success': True,
            'processing_time': processing_time,
            'preprocessing_result': result,
            'timestamp': start_time.isoformat()
        }

    except Exception as e:
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()

        error_msg = f"Failed to process {input_file.name}: {str(e)}"
        logger.error(error_msg)

        return {
            'input_file': str(input_file),
            'output_file': str(output_file),
            'success': False,
            'processing_time': processing_time,
            'error': error_msg,
            'timestamp': start_time.isoformat()
        }


def generate_processing_report(
    results: List[Dict[str, Any]],
    output_file: Path,
    logger: logging.Logger
) -> None:
    """Generate comprehensive processing report."""

    # Calculate statistics
    total_files = len(results)
    successful_files = sum(1 for r in results if r['success'])
    failed_files = total_files - successful_files
    total_processing_time = sum(r['processing_time'] for r in results)
    avg_processing_time = total_processing_time / total_files if total_files > 0 else 0

    # Group by participant
    participants = {}
    for result in results:
        input_path = Path(result['input_file'])
        # Extract participant ID from path (e.g., sub-ATL001)
        participant_parts = [p for p in input_path.parts if p.startswith('sub-')]
        if participant_parts:
            participant = participant_parts[0]
            if participant not in participants:
                participants[participant] = {'total': 0, 'successful': 0, 'failed': 0}
            participants[participant]['total'] += 1
            if result['success']:
                participants[participant]['successful'] += 1
            else:
                participants[participant]['failed'] += 1

    # Create comprehensive report
    report = {
        'preprocessing_report': {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_files': total_files,
                'successful_files': successful_files,
                'failed_files': failed_files,
                'success_rate': (successful_files / total_files * 100) if total_files > 0 else 0,
                'total_processing_time': total_processing_time,
                'average_processing_time': avg_processing_time
            },
            'participants': participants,
            'configuration': {
                'sample_rate': 16000,
                'channels': 1,
                'target_dbfs': -20.0,
                'noise_reduction': 0.8
            },
            'detailed_results': results
        }
    }

    # Save report
    output_file.parent.mkdir(parents=True, exist_ok=True)
    with open(output_file, 'w') as f:
        json.dump(report, f, indent=2)

    logger.info(f"Processing report saved to: {output_file}")
    logger.info(f"Summary: {successful_files}/{total_files} files processed successfully")
    logger.info(f"Total processing time: {total_processing_time:.2f}s")
    logger.info(f"Average processing time: {avg_processing_time:.2f}s per file")


def main():
    """Main preprocessing function."""
    parser = argparse.ArgumentParser(
        description='Comprehensive data preprocessing for AudioTechEquity',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    parser.add_argument(
        '--data-dir',
        type=Path,
        default=Path('audtecheq/data'),
        help='Source data directory (default: audtecheq/data)'
    )

    parser.add_argument(
        '--output-dir',
        type=Path,
        default=Path('audtecheq/data/derivative/preprocessed'),
        help='Output directory for preprocessed data (default: audtecheq/data/derivative/preprocessed)'
    )

    parser.add_argument(
        '--sample-rate',
        type=int,
        default=16000,
        help='Target sample rate in Hz (default: 16000)'
    )

    parser.add_argument(
        '--channels',
        type=int,
        choices=[1, 2],
        default=1,
        help='Target number of channels (default: 1)'
    )

    parser.add_argument(
        '--target-dbfs',
        type=float,
        default=-20.0,
        help='Target loudness in dBFS (default: -20.0)'
    )

    parser.add_argument(
        '--noise-reduction',
        type=float,
        default=0.8,
        help='Noise reduction strength 0.0-1.0 (default: 0.8)'
    )

    parser.add_argument(
        '--keep-intermediates',
        action='store_true',
        help='Keep intermediate processing files'
    )

    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level (default: INFO)'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be processed without actually processing'
    )

    args = parser.parse_args()

    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = f'preprocessing_log_{timestamp}.log'
    logger = setup_logging(args.log_level, log_file)

    logger.info("Starting comprehensive data preprocessing")
    logger.info(f"Data directory: {args.data_dir}")
    logger.info(f"Output directory: {args.output_dir}")
    logger.info(f"Configuration: sample_rate={args.sample_rate}, channels={args.channels}, "
                f"target_dbfs={args.target_dbfs}, noise_reduction={args.noise_reduction}")

    # Validate input directory
    if not args.data_dir.exists():
        logger.error(f"Data directory does not exist: {args.data_dir}")
        return 1

    # Find all audio files
    audio_files = find_audio_files(args.data_dir)
    logger.info(f"Found {len(audio_files)} audio files to process")

    if not audio_files:
        logger.warning("No audio files found in data directory")
        return 0

    # Create derivative directory structure
    derivative_dirs = create_derivative_structure(args.data_dir, args.output_dir.parent)
    logger.info(f"Created derivative directory structure at: {args.output_dir.parent}")

    # Preprocessing configuration
    config = {
        'sample_rate': args.sample_rate,
        'channels': args.channels,
        'target_dbfs': args.target_dbfs,
        'noise_reduction': args.noise_reduction,
        'keep_intermediates': args.keep_intermediates
    }

    if args.dry_run:
        logger.info("DRY RUN - showing files that would be processed:")
        for audio_file in audio_files:
            relative_path = audio_file.relative_to(args.data_dir)
            output_file = args.output_dir / relative_path
            logger.info(f"  {audio_file} -> {output_file}")
        return 0

    # Process all files
    results = []
    for i, audio_file in enumerate(audio_files, 1):
        logger.info(f"Processing file {i}/{len(audio_files)}: {audio_file.name}")

        # Determine output path (preserve directory structure)
        relative_path = audio_file.relative_to(args.data_dir)
        output_file = args.output_dir / relative_path

        # Process the file
        result = preprocess_file_direct(audio_file, output_file, config, logger)
        results.append(result)

    # Generate comprehensive report
    report_file = derivative_dirs['reports'] / f'preprocessing_report_{timestamp}.json'
    generate_processing_report(results, report_file, logger)

    # Print summary
    successful_files = sum(1 for r in results if r['success'])
    logger.info(f"Preprocessing completed!")
    logger.info(f"Successfully processed: {successful_files}/{len(results)} files")
    logger.info(f"Log file: {log_file}")
    logger.info(f"Report file: {report_file}")

    return 0 if successful_files == len(results) else 1


if __name__ == '__main__':
    sys.exit(main())
