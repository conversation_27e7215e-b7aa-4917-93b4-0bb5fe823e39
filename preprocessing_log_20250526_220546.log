2025-05-26 22:05:46,023 - __main__ - INFO - Starting comprehensive data preprocessing
2025-05-26 22:05:46,023 - __main__ - INFO - Data directory: audtecheq/data
2025-05-26 22:05:46,023 - __main__ - INFO - Output directory: audtecheq/data/derivative/preprocessed
2025-05-26 22:05:46,023 - __main__ - INFO - Configuration: sample_rate=16000, channels=1, target_dbfs=-20.0, noise_reduction=0.8
2025-05-26 22:05:46,027 - __main__ - INFO - Found 109 audio files to process
2025-05-26 22:05:46,027 - __main__ - INFO - Created derivative directory structure at: audtecheq/data/derivative
2025-05-26 22:05:46,027 - __main__ - INFO - Processing file 1/109: sub-ATL001_task-FTL_acq-MicX.wav
2025-05-26 22:05:53,779 - __main__ - INFO - Processing file 2/109: sub-ATL001_task-FTL_acq-MicX.wav
2025-05-26 22:06:02,088 - __main__ - INFO - Processing file 3/109: sub-ATL001_task-FTL_acq-MicXY_normalized.wav
2025-05-26 22:06:11,018 - __main__ - INFO - Processing file 4/109: sub-ATL001_task-FTL_acq-MicXY_resampled.wav
2025-05-26 22:06:20,181 - __main__ - INFO - Processing file 5/109: sub-ATL001_task-FTL_acq-MicX.wav
2025-05-26 22:06:51,971 - __main__ - INFO - Processing file 6/109: sub-ATL001_task-FTL_acq-MicXY.wav
2025-05-26 22:07:22,533 - __main__ - INFO - Processing file 7/109: sub-ATL001_task-FTL_acq-MicY.wav
2025-05-26 22:07:53,543 - __main__ - INFO - Processing file 8/109: sub-ATL001_task-FTL_acq-ZA.wav
2025-05-26 22:08:05,119 - __main__ - INFO - Processing file 9/109: sub-ATL002_task-celfp3.screen_acq-MicX.wav
2025-05-26 22:08:08,513 - __main__ - INFO - Processing file 10/109: sub-ATL002_task-celfp3.screen_acq-MicXY.wav
2025-05-26 22:08:11,906 - __main__ - INFO - Processing file 11/109: sub-ATL002_task-celfp3.screen_acq-MicY.wav
2025-05-26 22:08:15,202 - __main__ - INFO - Processing file 12/109: sub-ATL002_task-gfta.test_acq-MicX.wav
2025-05-26 22:08:20,850 - __main__ - INFO - Processing file 13/109: sub-ATL002_task-gfta.test_acq-MicXY.wav
2025-05-26 22:08:26,366 - __main__ - INFO - Processing file 14/109: sub-ATL002_task-gfta.test_acq-MicY.wav
2025-05-26 22:08:31,785 - __main__ - INFO - Processing file 15/109: sub-ATL002_task-picturetask_acq-MicX.wav
2025-05-26 22:08:35,282 - __main__ - INFO - Processing file 16/109: sub-ATL002_task-picturetask_acq-MicXY.wav
2025-05-26 22:08:38,779 - __main__ - INFO - Processing file 17/109: sub-ATL002_task-picturetask_acq-MicY.wav
2025-05-26 22:08:42,301 - __main__ - INFO - Processing file 18/109: sub-ATL002_task-pls5.screen_acq-MicX.wav
2025-05-26 22:08:46,254 - __main__ - INFO - Processing file 19/109: sub-ATL002_task-pls5.screen_acq-MicXY.wav
2025-05-26 22:08:50,322 - __main__ - INFO - Processing file 20/109: sub-ATL002_task-pls5.screen_acq-MicY.wav
2025-05-26 22:08:54,223 - __main__ - INFO - Processing file 21/109: sub-ATL002_task-toyplay_acq-MicX.wav
2025-05-26 22:08:58,893 - __main__ - INFO - Processing file 22/109: sub-ATL002_task-toyplay_acq-MicXY.wav
2025-05-26 22:09:03,421 - __main__ - INFO - Processing file 23/109: sub-ATL002_task-toyplay_acq-MicY.wav
2025-05-26 22:09:08,261 - __main__ - INFO - Processing file 24/109: sub-ATL002_task-warmup_acq-MicX.wav
2025-05-26 22:09:11,481 - __main__ - INFO - Processing file 25/109: sub-ATL002_task-warmup_acq-MicXY.wav
2025-05-26 22:09:14,582 - __main__ - INFO - Processing file 26/109: sub-ATL002_task-warmup_acq-MicY.wav
2025-05-26 22:09:17,663 - __main__ - INFO - Processing file 27/109: sub-ATL003_task-celfp3.screen_acq-MicX.wav
2025-05-26 22:09:21,679 - __main__ - INFO - Processing file 28/109: sub-ATL003_task-celfp3.screen_acq-MicXY.wav
2025-05-26 22:09:25,689 - __main__ - INFO - Processing file 29/109: sub-ATL003_task-celfp3.screen_acq-MicY.wav
2025-05-26 22:09:29,699 - __main__ - INFO - Processing file 30/109: sub-ATL003_task-celfp3.screen_acq-ZA.wav
2025-05-26 22:09:30,990 - __main__ - INFO - Processing file 31/109: sub-ATL003_task-celfp3.test_acq-MicX.wav
2025-05-26 22:09:40,461 - __main__ - INFO - Processing file 32/109: sub-ATL003_task-celfp3.test_acq-MicY.wav
2025-05-26 22:09:50,074 - __main__ - INFO - Processing file 33/109: sub-ATL003_task-celfp3.test_acq-ZA.wav
2025-05-26 22:09:53,107 - __main__ - INFO - Processing file 34/109: sub-ATL003_task-gfta.test_acq-MicX.wav
2025-05-26 22:10:00,450 - __main__ - INFO - Processing file 35/109: sub-ATL003_task-gfta.test_acq-MicXY.wav
2025-05-26 22:10:08,241 - __main__ - INFO - Processing file 36/109: sub-ATL003_task-gfta.test_acq-MicY.wav
2025-05-26 22:10:15,952 - __main__ - INFO - Processing file 37/109: sub-ATL003_task-gfta.test_acq-ZA.wav
2025-05-26 22:10:18,587 - __main__ - INFO - Processing file 38/109: sub-ATL003_task-picturetask.2_acq-MicX.wav
2025-05-26 22:10:19,945 - __main__ - INFO - Processing file 39/109: sub-ATL003_task-picturetask.2_acq-MicXY.wav
2025-05-26 22:10:21,286 - __main__ - INFO - Processing file 40/109: sub-ATL003_task-picturetask.2_acq-MicY.wav
2025-05-26 22:10:22,616 - __main__ - INFO - Processing file 41/109: sub-ATL003_task-picturetask.2_acq-ZA.wav
2025-05-26 22:10:23,630 - __main__ - INFO - Processing file 42/109: sub-ATL003_task-picturetask_acq-MicX.wav
2025-05-26 22:10:27,036 - __main__ - INFO - Processing file 43/109: sub-ATL003_task-picturetask_acq-MicXY.wav
2025-05-26 22:10:30,202 - __main__ - INFO - Processing file 44/109: sub-ATL003_task-picturetask_acq-MicY.wav
2025-05-26 22:10:33,359 - __main__ - INFO - Processing file 45/109: sub-ATL003_task-picturetask_acq-ZA.wav
2025-05-26 22:10:34,466 - __main__ - INFO - Processing file 46/109: sub-ATL003_task-pls5.screen_acq-MicX.wav
2025-05-26 22:10:39,001 - __main__ - INFO - Processing file 47/109: sub-ATL003_task-pls5.screen_acq-MicXY.wav
2025-05-26 22:10:43,257 - __main__ - INFO - Processing file 48/109: sub-ATL003_task-pls5.screen_acq-MicY.wav
2025-05-26 22:10:47,488 - __main__ - INFO - Processing file 49/109: sub-ATL003_task-pls5.screen_acq-ZA.wav
2025-05-26 22:10:49,045 - __main__ - INFO - Processing file 50/109: sub-ATL003_task-toyplay_acq-MicX.wav
2025-05-26 22:10:53,390 - __main__ - INFO - Processing file 51/109: sub-ATL003_task-toyplay_acq-MicXY.wav
2025-05-26 22:10:57,707 - __main__ - INFO - Processing file 52/109: sub-ATL003_task-toyplay_acq-MicY.wav
2025-05-26 22:11:02,102 - __main__ - INFO - Processing file 53/109: sub-ATL003_task-toyplay_acq-ZA.wav
2025-05-26 22:11:02,498 - __main__ - INFO - Processing file 54/109: sub-ATL003_task-warmup_acq-MicX.wav
2025-05-26 22:11:05,078 - __main__ - INFO - Processing file 55/109: sub-ATL003_task-warmup_acq-MicXY.wav
2025-05-26 22:11:07,740 - __main__ - INFO - Processing file 56/109: sub-ATL003_task-warmup_acq-MicY.wav
2025-05-26 22:11:10,377 - __main__ - INFO - Processing file 57/109: sub-ATL003_task-warmup_acq-ZA.wav
2025-05-26 22:11:11,323 - __main__ - INFO - Processing file 58/109: sub-ATL004_task-gfta.test_acq-MicX.wav
2025-05-26 22:11:18,915 - __main__ - INFO - Processing file 59/109: sub-ATL004_task-gfta.test_acq-MicXY.wav
2025-05-26 22:11:26,597 - __main__ - INFO - Processing file 60/109: sub-ATL004_task-gfta.test_acq-MicY.wav
2025-05-26 22:11:34,139 - __main__ - INFO - Processing file 61/109: sub-ATL004_task-gfta.test_acq-ZA.wav
2025-05-26 22:11:36,614 - __main__ - INFO - Processing file 62/109: sub-ATL004_task-picturetask_acq-MicX.wav
2025-05-26 22:11:41,396 - __main__ - INFO - Processing file 63/109: sub-ATL004_task-picturetask_acq-MicXY.wav
2025-05-26 22:11:46,005 - __main__ - INFO - Processing file 64/109: sub-ATL004_task-picturetask_acq-MicY.wav
2025-05-26 22:11:50,667 - __main__ - INFO - Processing file 65/109: sub-ATL004_task-picturetask_acq-ZA.wav
2025-05-26 22:11:52,157 - __main__ - INFO - Processing file 66/109: sub-ATL004_task-pls5.screen_acq-MicX.wav
2025-05-26 22:11:56,926 - __main__ - INFO - Processing file 67/109: sub-ATL004_task-pls5.screen_acq-MicXY.wav
2025-05-26 22:12:01,852 - __main__ - INFO - Processing file 68/109: sub-ATL004_task-pls5.screen_acq-MicY.wav
2025-05-26 22:12:06,619 - __main__ - INFO - Processing file 69/109: sub-ATL004_task-pls5.screen_acq-ZA.wav
2025-05-26 22:12:08,208 - __main__ - INFO - Processing file 70/109: sub-ATL004_task-toyplay_acq-MicX.wav
2025-05-26 22:12:14,351 - __main__ - INFO - Processing file 71/109: sub-ATL004_task-toyplay_acq-MicXY.wav
2025-05-26 22:12:20,343 - __main__ - INFO - Processing file 72/109: sub-ATL004_task-toyplay_acq-MicY.wav
2025-05-26 22:12:26,062 - __main__ - INFO - Processing file 73/109: sub-ATL004_task-toyplay_acq-ZA.wav
2025-05-26 22:12:27,789 - __main__ - INFO - Processing file 74/109: sub-ATL004_task-warmup_acq-MicX.wav
2025-05-26 22:12:34,587 - __main__ - INFO - Processing file 75/109: sub-ATL004_task-warmup_acq-MicXY.wav
2025-05-26 22:12:41,373 - __main__ - INFO - Processing file 76/109: sub-ATL004_task-warmup_acq-MicY.wav
2025-05-26 22:12:48,375 - __main__ - INFO - Processing file 77/109: sub-ATL004_task-warmup_acq-ZA.wav
2025-05-26 22:12:50,526 - __main__ - INFO - Processing file 78/109: sub-ATL005_task-celfp3.screen_acq-MicX.wav
2025-05-26 22:12:53,535 - __main__ - INFO - Processing file 79/109: sub-ATL005_task-celfp3.screen_acq-MicXY.wav
2025-05-26 22:12:56,559 - __main__ - INFO - Processing file 80/109: sub-ATL005_task-celfp3.screen_acq-MicY.wav
2025-05-26 22:12:59,696 - __main__ - INFO - Processing file 81/109: sub-ATL005_task-celfp3.screen_acq-ZA.wav
2025-05-26 22:13:00,683 - __main__ - INFO - Processing file 82/109: sub-ATL005_task-celfp3.test.2_acq-MicX.wav
2025-05-26 22:13:15,833 - __main__ - INFO - Processing file 83/109: sub-ATL005_task-celfp3.test.2_acq-MicXY.wav
2025-05-26 22:13:24,623 - __main__ - INFO - Processing file 84/109: sub-ATL005_task-celfp3.test.2_acq-MicY.wav
2025-05-26 22:13:39,831 - __main__ - INFO - Processing file 85/109: sub-ATL005_task-celfp3.test.NA_acq-ZA.wav
2025-05-26 22:13:42,137 - __main__ - INFO - Processing file 86/109: sub-ATL005_task-celfp3.test_acq-MicX.wav
2025-05-26 22:14:03,870 - __main__ - INFO - Processing file 87/109: sub-ATL005_task-celfp3.test_acq-MicXY.wav
2025-05-26 22:14:26,010 - __main__ - INFO - Processing file 88/109: sub-ATL005_task-celfp3.test_acq-MicY.wav
2025-05-26 22:14:46,617 - __main__ - INFO - Processing file 89/109: sub-ATL005_task-celfp3.test_acq-ZA.wav
2025-05-26 22:14:48,047 - __main__ - INFO - Processing file 90/109: sub-ATL005_task-gfta.test_acq-MicX.wav
2025-05-26 22:14:53,118 - __main__ - INFO - Processing file 91/109: sub-ATL005_task-gfta.test_acq-MicXY.wav
2025-05-26 22:14:58,134 - __main__ - INFO - Processing file 92/109: sub-ATL005_task-gfta.test_acq-MicY.wav
2025-05-26 22:15:03,151 - __main__ - INFO - Processing file 93/109: sub-ATL005_task-gfta.test_acq-ZA.wav
2025-05-26 22:15:04,729 - __main__ - INFO - Processing file 94/109: sub-ATL005_task-picturetask_acq-MicX.wav
2025-05-26 22:15:08,566 - __main__ - INFO - Processing file 95/109: sub-ATL005_task-picturetask_acq-MicXY.wav
2025-05-26 22:15:12,495 - __main__ - INFO - Processing file 96/109: sub-ATL005_task-picturetask_acq-MicY.wav
2025-05-26 22:15:16,377 - __main__ - INFO - Processing file 97/109: sub-ATL005_task-picturetask_acq-ZA.wav
2025-05-26 22:15:17,539 - __main__ - INFO - Processing file 98/109: sub-ATL005_task-pls5.screen_acq-MicX.wav
2025-05-26 22:15:21,013 - __main__ - INFO - Processing file 99/109: sub-ATL005_task-pls5.screen_acq-MicXY.wav
2025-05-26 22:15:24,430 - __main__ - INFO - Processing file 100/109: sub-ATL005_task-pls5.screen_acq-MicY.wav
2025-05-26 22:15:27,842 - __main__ - INFO - Processing file 101/109: sub-ATL005_task-pls5.screen_acq-ZA.wav
2025-05-26 22:15:29,012 - __main__ - INFO - Processing file 102/109: sub-ATL005_task-toyplay_acq-MicX.wav
2025-05-26 22:15:33,574 - __main__ - INFO - Processing file 103/109: sub-ATL005_task-toyplay_acq-MicXY.wav
2025-05-26 22:15:38,029 - __main__ - INFO - Processing file 104/109: sub-ATL005_task-toyplay_acq-MicY.wav
2025-05-26 22:15:42,559 - __main__ - INFO - Processing file 105/109: sub-ATL005_task-toyplay_acq-ZA.wav
2025-05-26 22:15:44,032 - __main__ - INFO - Processing file 106/109: sub-ATL005_task-warmup_acq-MicX.wav
2025-05-26 22:15:46,293 - __main__ - INFO - Processing file 107/109: sub-ATL005_task-warmup_acq-MicXY.wav
2025-05-26 22:15:48,520 - __main__ - INFO - Processing file 108/109: sub-ATL005_task-warmup_acq-MicY.wav
2025-05-26 22:15:50,763 - __main__ - INFO - Processing file 109/109: sub-ATL005_task-warmup_acq-ZA.wav
2025-05-26 22:15:51,471 - __main__ - INFO - Processing report saved to: audtecheq/data/derivative/reports/preprocessing_report_20250526_220546.json
2025-05-26 22:15:51,471 - __main__ - INFO - Summary: 109/109 files processed successfully
2025-05-26 22:15:51,471 - __main__ - INFO - Total processing time: 605.43s
2025-05-26 22:15:51,471 - __main__ - INFO - Average processing time: 5.55s per file
2025-05-26 22:15:51,471 - __main__ - INFO - Preprocessing completed!
2025-05-26 22:15:51,471 - __main__ - INFO - Successfully processed: 109/109 files
2025-05-26 22:15:51,471 - __main__ - INFO - Log file: preprocessing_log_20250526_220546.log
2025-05-26 22:15:51,471 - __main__ - INFO - Report file: audtecheq/data/derivative/reports/preprocessing_report_20250526_220546.json
