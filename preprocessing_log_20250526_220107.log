2025-05-26 22:01:07,751 - __main__ - INFO - Starting comprehensive data preprocessing
2025-05-26 22:01:07,751 - __main__ - INFO - Data directory: audtecheq/data
2025-05-26 22:01:07,751 - __main__ - INFO - Output directory: audtecheq/data/derivative/preprocessed
2025-05-26 22:01:07,751 - __main__ - INFO - Configuration: sample_rate=16000, channels=1, target_dbfs=-20.0, noise_reduction=0.8
2025-05-26 22:01:07,754 - __main__ - INFO - Found 106 audio files to process
2025-05-26 22:01:07,754 - __main__ - INFO - Created derivative directory structure at: audtecheq/data/derivative
2025-05-26 22:01:07,754 - __main__ - INFO - Processing file 1/106: sub-ATL001_task-FTL_acq-MicX.wav
2025-05-26 22:01:07,754 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/derivative/preprocessed/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/derivative/preprocessed/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,763 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,764 - __main__ - ERROR - Failed to process sub-ATL001_task-FTL_acq-MicX.wav
2025-05-26 22:01:07,764 - __main__ - INFO - Processing file 2/106: sub-ATL001_task-FTL_acq-MicX.wav
2025-05-26 22:01:07,764 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,769 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,769 - __main__ - ERROR - Failed to process sub-ATL001_task-FTL_acq-MicX.wav
2025-05-26 22:01:07,769 - __main__ - INFO - Processing file 3/106: sub-ATL001_task-FTL_acq-MicXY.wav
2025-05-26 22:01:07,769 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,774 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,774 - __main__ - ERROR - Failed to process sub-ATL001_task-FTL_acq-MicXY.wav
2025-05-26 22:01:07,774 - __main__ - INFO - Processing file 4/106: sub-ATL001_task-FTL_acq-MicY.wav
2025-05-26 22:01:07,775 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL001/beh/sub-ATL001_task-FTL_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,780 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,781 - __main__ - ERROR - Failed to process sub-ATL001_task-FTL_acq-MicY.wav
2025-05-26 22:01:07,781 - __main__ - INFO - Processing file 5/106: sub-ATL001_task-FTL_acq-ZA.wav
2025-05-26 22:01:07,781 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL001/beh/sub-ATL001_task-FTL_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL001/beh/sub-ATL001_task-FTL_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,785 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,785 - __main__ - ERROR - Failed to process sub-ATL001_task-FTL_acq-ZA.wav
2025-05-26 22:01:07,785 - __main__ - INFO - Processing file 6/106: sub-ATL002_task-celfp3.screen_acq-MicX.wav
2025-05-26 22:01:07,785 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-celfp3.screen_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-celfp3.screen_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,789 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,789 - __main__ - ERROR - Failed to process sub-ATL002_task-celfp3.screen_acq-MicX.wav
2025-05-26 22:01:07,789 - __main__ - INFO - Processing file 7/106: sub-ATL002_task-celfp3.screen_acq-MicXY.wav
2025-05-26 22:01:07,790 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-celfp3.screen_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-celfp3.screen_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,794 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,794 - __main__ - ERROR - Failed to process sub-ATL002_task-celfp3.screen_acq-MicXY.wav
2025-05-26 22:01:07,794 - __main__ - INFO - Processing file 8/106: sub-ATL002_task-celfp3.screen_acq-MicY.wav
2025-05-26 22:01:07,794 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-celfp3.screen_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-celfp3.screen_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,803 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,804 - __main__ - ERROR - Failed to process sub-ATL002_task-celfp3.screen_acq-MicY.wav
2025-05-26 22:01:07,805 - __main__ - INFO - Processing file 9/106: sub-ATL002_task-gfta.test_acq-MicX.wav
2025-05-26 22:01:07,805 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-gfta.test_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-gfta.test_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,812 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,812 - __main__ - ERROR - Failed to process sub-ATL002_task-gfta.test_acq-MicX.wav
2025-05-26 22:01:07,812 - __main__ - INFO - Processing file 10/106: sub-ATL002_task-gfta.test_acq-MicXY.wav
2025-05-26 22:01:07,813 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-gfta.test_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-gfta.test_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,820 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,821 - __main__ - ERROR - Failed to process sub-ATL002_task-gfta.test_acq-MicXY.wav
2025-05-26 22:01:07,821 - __main__ - INFO - Processing file 11/106: sub-ATL002_task-gfta.test_acq-MicY.wav
2025-05-26 22:01:07,821 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-gfta.test_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-gfta.test_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,827 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,829 - __main__ - ERROR - Failed to process sub-ATL002_task-gfta.test_acq-MicY.wav
2025-05-26 22:01:07,829 - __main__ - INFO - Processing file 12/106: sub-ATL002_task-picturetask_acq-MicX.wav
2025-05-26 22:01:07,829 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-picturetask_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-picturetask_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,834 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,839 - __main__ - ERROR - Failed to process sub-ATL002_task-picturetask_acq-MicX.wav
2025-05-26 22:01:07,839 - __main__ - INFO - Processing file 13/106: sub-ATL002_task-picturetask_acq-MicXY.wav
2025-05-26 22:01:07,839 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-picturetask_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-picturetask_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,844 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,845 - __main__ - ERROR - Failed to process sub-ATL002_task-picturetask_acq-MicXY.wav
2025-05-26 22:01:07,845 - __main__ - INFO - Processing file 14/106: sub-ATL002_task-picturetask_acq-MicY.wav
2025-05-26 22:01:07,845 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-picturetask_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-picturetask_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,850 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,850 - __main__ - ERROR - Failed to process sub-ATL002_task-picturetask_acq-MicY.wav
2025-05-26 22:01:07,850 - __main__ - INFO - Processing file 15/106: sub-ATL002_task-pls5.screen_acq-MicX.wav
2025-05-26 22:01:07,850 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-pls5.screen_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-pls5.screen_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,855 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,855 - __main__ - ERROR - Failed to process sub-ATL002_task-pls5.screen_acq-MicX.wav
2025-05-26 22:01:07,855 - __main__ - INFO - Processing file 16/106: sub-ATL002_task-pls5.screen_acq-MicXY.wav
2025-05-26 22:01:07,856 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-pls5.screen_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-pls5.screen_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,861 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,861 - __main__ - ERROR - Failed to process sub-ATL002_task-pls5.screen_acq-MicXY.wav
2025-05-26 22:01:07,861 - __main__ - INFO - Processing file 17/106: sub-ATL002_task-pls5.screen_acq-MicY.wav
2025-05-26 22:01:07,861 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-pls5.screen_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-pls5.screen_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,866 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,866 - __main__ - ERROR - Failed to process sub-ATL002_task-pls5.screen_acq-MicY.wav
2025-05-26 22:01:07,866 - __main__ - INFO - Processing file 18/106: sub-ATL002_task-toyplay_acq-MicX.wav
2025-05-26 22:01:07,867 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-toyplay_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-toyplay_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,872 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,872 - __main__ - ERROR - Failed to process sub-ATL002_task-toyplay_acq-MicX.wav
2025-05-26 22:01:07,872 - __main__ - INFO - Processing file 19/106: sub-ATL002_task-toyplay_acq-MicXY.wav
2025-05-26 22:01:07,872 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-toyplay_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-toyplay_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,877 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,878 - __main__ - ERROR - Failed to process sub-ATL002_task-toyplay_acq-MicXY.wav
2025-05-26 22:01:07,878 - __main__ - INFO - Processing file 20/106: sub-ATL002_task-toyplay_acq-MicY.wav
2025-05-26 22:01:07,878 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-toyplay_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-toyplay_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,883 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,883 - __main__ - ERROR - Failed to process sub-ATL002_task-toyplay_acq-MicY.wav
2025-05-26 22:01:07,883 - __main__ - INFO - Processing file 21/106: sub-ATL002_task-warmup_acq-MicX.wav
2025-05-26 22:01:07,883 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-warmup_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-warmup_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,888 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,888 - __main__ - ERROR - Failed to process sub-ATL002_task-warmup_acq-MicX.wav
2025-05-26 22:01:07,888 - __main__ - INFO - Processing file 22/106: sub-ATL002_task-warmup_acq-MicXY.wav
2025-05-26 22:01:07,888 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-warmup_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-warmup_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,892 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,892 - __main__ - ERROR - Failed to process sub-ATL002_task-warmup_acq-MicXY.wav
2025-05-26 22:01:07,892 - __main__ - INFO - Processing file 23/106: sub-ATL002_task-warmup_acq-MicY.wav
2025-05-26 22:01:07,892 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL002/beh/sub-ATL002_task-warmup_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL002/beh/sub-ATL002_task-warmup_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,896 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,903 - __main__ - ERROR - Failed to process sub-ATL002_task-warmup_acq-MicY.wav
2025-05-26 22:01:07,904 - __main__ - INFO - Processing file 24/106: sub-ATL003_task-celfp3.screen_acq-MicX.wav
2025-05-26 22:01:07,904 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,908 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,909 - __main__ - ERROR - Failed to process sub-ATL003_task-celfp3.screen_acq-MicX.wav
2025-05-26 22:01:07,909 - __main__ - INFO - Processing file 25/106: sub-ATL003_task-celfp3.screen_acq-MicXY.wav
2025-05-26 22:01:07,909 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,913 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,913 - __main__ - ERROR - Failed to process sub-ATL003_task-celfp3.screen_acq-MicXY.wav
2025-05-26 22:01:07,913 - __main__ - INFO - Processing file 26/106: sub-ATL003_task-celfp3.screen_acq-MicY.wav
2025-05-26 22:01:07,913 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,917 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,917 - __main__ - ERROR - Failed to process sub-ATL003_task-celfp3.screen_acq-MicY.wav
2025-05-26 22:01:07,917 - __main__ - INFO - Processing file 27/106: sub-ATL003_task-celfp3.screen_acq-ZA.wav
2025-05-26 22:01:07,917 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-celfp3.screen_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,920 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,921 - __main__ - ERROR - Failed to process sub-ATL003_task-celfp3.screen_acq-ZA.wav
2025-05-26 22:01:07,921 - __main__ - INFO - Processing file 28/106: sub-ATL003_task-celfp3.test_acq-MicX.wav
2025-05-26 22:01:07,921 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-celfp3.test_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-celfp3.test_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,924 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,924 - __main__ - ERROR - Failed to process sub-ATL003_task-celfp3.test_acq-MicX.wav
2025-05-26 22:01:07,924 - __main__ - INFO - Processing file 29/106: sub-ATL003_task-celfp3.test_acq-MicY.wav
2025-05-26 22:01:07,924 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-celfp3.test_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-celfp3.test_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,927 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,928 - __main__ - ERROR - Failed to process sub-ATL003_task-celfp3.test_acq-MicY.wav
2025-05-26 22:01:07,928 - __main__ - INFO - Processing file 30/106: sub-ATL003_task-celfp3.test_acq-ZA.wav
2025-05-26 22:01:07,928 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-celfp3.test_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-celfp3.test_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,932 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,932 - __main__ - ERROR - Failed to process sub-ATL003_task-celfp3.test_acq-ZA.wav
2025-05-26 22:01:07,932 - __main__ - INFO - Processing file 31/106: sub-ATL003_task-gfta.test_acq-MicX.wav
2025-05-26 22:01:07,932 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,936 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,936 - __main__ - ERROR - Failed to process sub-ATL003_task-gfta.test_acq-MicX.wav
2025-05-26 22:01:07,936 - __main__ - INFO - Processing file 32/106: sub-ATL003_task-gfta.test_acq-MicXY.wav
2025-05-26 22:01:07,936 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,941 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,941 - __main__ - ERROR - Failed to process sub-ATL003_task-gfta.test_acq-MicXY.wav
2025-05-26 22:01:07,941 - __main__ - INFO - Processing file 33/106: sub-ATL003_task-gfta.test_acq-MicY.wav
2025-05-26 22:01:07,942 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,946 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,946 - __main__ - ERROR - Failed to process sub-ATL003_task-gfta.test_acq-MicY.wav
2025-05-26 22:01:07,946 - __main__ - INFO - Processing file 34/106: sub-ATL003_task-gfta.test_acq-ZA.wav
2025-05-26 22:01:07,946 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-gfta.test_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,950 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,950 - __main__ - ERROR - Failed to process sub-ATL003_task-gfta.test_acq-ZA.wav
2025-05-26 22:01:07,950 - __main__ - INFO - Processing file 35/106: sub-ATL003_task-picturetask.2_acq-MicX.wav
2025-05-26 22:01:07,950 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,954 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,955 - __main__ - ERROR - Failed to process sub-ATL003_task-picturetask.2_acq-MicX.wav
2025-05-26 22:01:07,955 - __main__ - INFO - Processing file 36/106: sub-ATL003_task-picturetask.2_acq-MicXY.wav
2025-05-26 22:01:07,955 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,960 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,962 - __main__ - ERROR - Failed to process sub-ATL003_task-picturetask.2_acq-MicXY.wav
2025-05-26 22:01:07,962 - __main__ - INFO - Processing file 37/106: sub-ATL003_task-picturetask.2_acq-MicY.wav
2025-05-26 22:01:07,962 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,967 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,968 - __main__ - ERROR - Failed to process sub-ATL003_task-picturetask.2_acq-MicY.wav
2025-05-26 22:01:07,968 - __main__ - INFO - Processing file 38/106: sub-ATL003_task-picturetask.2_acq-ZA.wav
2025-05-26 22:01:07,968 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask.2_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,973 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,973 - __main__ - ERROR - Failed to process sub-ATL003_task-picturetask.2_acq-ZA.wav
2025-05-26 22:01:07,973 - __main__ - INFO - Processing file 39/106: sub-ATL003_task-picturetask_acq-MicX.wav
2025-05-26 22:01:07,974 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,978 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,978 - __main__ - ERROR - Failed to process sub-ATL003_task-picturetask_acq-MicX.wav
2025-05-26 22:01:07,978 - __main__ - INFO - Processing file 40/106: sub-ATL003_task-picturetask_acq-MicXY.wav
2025-05-26 22:01:07,978 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,982 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,983 - __main__ - ERROR - Failed to process sub-ATL003_task-picturetask_acq-MicXY.wav
2025-05-26 22:01:07,983 - __main__ - INFO - Processing file 41/106: sub-ATL003_task-picturetask_acq-MicY.wav
2025-05-26 22:01:07,983 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,986 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,986 - __main__ - ERROR - Failed to process sub-ATL003_task-picturetask_acq-MicY.wav
2025-05-26 22:01:07,986 - __main__ - INFO - Processing file 42/106: sub-ATL003_task-picturetask_acq-ZA.wav
2025-05-26 22:01:07,986 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-picturetask_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,990 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,990 - __main__ - ERROR - Failed to process sub-ATL003_task-picturetask_acq-ZA.wav
2025-05-26 22:01:07,990 - __main__ - INFO - Processing file 43/106: sub-ATL003_task-pls5.screen_acq-MicX.wav
2025-05-26 22:01:07,991 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:07,996 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:07,996 - __main__ - ERROR - Failed to process sub-ATL003_task-pls5.screen_acq-MicX.wav
2025-05-26 22:01:07,996 - __main__ - INFO - Processing file 44/106: sub-ATL003_task-pls5.screen_acq-MicXY.wav
2025-05-26 22:01:07,996 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,001 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,001 - __main__ - ERROR - Failed to process sub-ATL003_task-pls5.screen_acq-MicXY.wav
2025-05-26 22:01:08,001 - __main__ - INFO - Processing file 45/106: sub-ATL003_task-pls5.screen_acq-MicY.wav
2025-05-26 22:01:08,002 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,006 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,007 - __main__ - ERROR - Failed to process sub-ATL003_task-pls5.screen_acq-MicY.wav
2025-05-26 22:01:08,007 - __main__ - INFO - Processing file 46/106: sub-ATL003_task-pls5.screen_acq-ZA.wav
2025-05-26 22:01:08,007 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-pls5.screen_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,011 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,012 - __main__ - ERROR - Failed to process sub-ATL003_task-pls5.screen_acq-ZA.wav
2025-05-26 22:01:08,012 - __main__ - INFO - Processing file 47/106: sub-ATL003_task-toyplay_acq-MicX.wav
2025-05-26 22:01:08,012 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,015 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,015 - __main__ - ERROR - Failed to process sub-ATL003_task-toyplay_acq-MicX.wav
2025-05-26 22:01:08,015 - __main__ - INFO - Processing file 48/106: sub-ATL003_task-toyplay_acq-MicXY.wav
2025-05-26 22:01:08,016 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,019 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,019 - __main__ - ERROR - Failed to process sub-ATL003_task-toyplay_acq-MicXY.wav
2025-05-26 22:01:08,019 - __main__ - INFO - Processing file 49/106: sub-ATL003_task-toyplay_acq-MicY.wav
2025-05-26 22:01:08,019 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,025 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,026 - __main__ - ERROR - Failed to process sub-ATL003_task-toyplay_acq-MicY.wav
2025-05-26 22:01:08,026 - __main__ - INFO - Processing file 50/106: sub-ATL003_task-toyplay_acq-ZA.wav
2025-05-26 22:01:08,026 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-toyplay_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,031 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,031 - __main__ - ERROR - Failed to process sub-ATL003_task-toyplay_acq-ZA.wav
2025-05-26 22:01:08,031 - __main__ - INFO - Processing file 51/106: sub-ATL003_task-warmup_acq-MicX.wav
2025-05-26 22:01:08,031 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-warmup_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-warmup_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,034 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,035 - __main__ - ERROR - Failed to process sub-ATL003_task-warmup_acq-MicX.wav
2025-05-26 22:01:08,035 - __main__ - INFO - Processing file 52/106: sub-ATL003_task-warmup_acq-MicXY.wav
2025-05-26 22:01:08,035 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-warmup_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-warmup_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,040 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,040 - __main__ - ERROR - Failed to process sub-ATL003_task-warmup_acq-MicXY.wav
2025-05-26 22:01:08,040 - __main__ - INFO - Processing file 53/106: sub-ATL003_task-warmup_acq-MicY.wav
2025-05-26 22:01:08,040 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-warmup_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-warmup_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,044 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,044 - __main__ - ERROR - Failed to process sub-ATL003_task-warmup_acq-MicY.wav
2025-05-26 22:01:08,044 - __main__ - INFO - Processing file 54/106: sub-ATL003_task-warmup_acq-ZA.wav
2025-05-26 22:01:08,044 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL003/beh/sub-ATL003_task-warmup_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL003/beh/sub-ATL003_task-warmup_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,047 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,047 - __main__ - ERROR - Failed to process sub-ATL003_task-warmup_acq-ZA.wav
2025-05-26 22:01:08,048 - __main__ - INFO - Processing file 55/106: sub-ATL004_task-gfta.test_acq-MicX.wav
2025-05-26 22:01:08,048 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,051 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,051 - __main__ - ERROR - Failed to process sub-ATL004_task-gfta.test_acq-MicX.wav
2025-05-26 22:01:08,051 - __main__ - INFO - Processing file 56/106: sub-ATL004_task-gfta.test_acq-MicXY.wav
2025-05-26 22:01:08,051 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,056 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,056 - __main__ - ERROR - Failed to process sub-ATL004_task-gfta.test_acq-MicXY.wav
2025-05-26 22:01:08,056 - __main__ - INFO - Processing file 57/106: sub-ATL004_task-gfta.test_acq-MicY.wav
2025-05-26 22:01:08,056 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,061 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,062 - __main__ - ERROR - Failed to process sub-ATL004_task-gfta.test_acq-MicY.wav
2025-05-26 22:01:08,062 - __main__ - INFO - Processing file 58/106: sub-ATL004_task-gfta.test_acq-ZA.wav
2025-05-26 22:01:08,062 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-gfta.test_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,066 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,066 - __main__ - ERROR - Failed to process sub-ATL004_task-gfta.test_acq-ZA.wav
2025-05-26 22:01:08,066 - __main__ - INFO - Processing file 59/106: sub-ATL004_task-picturetask_acq-MicX.wav
2025-05-26 22:01:08,066 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,070 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,070 - __main__ - ERROR - Failed to process sub-ATL004_task-picturetask_acq-MicX.wav
2025-05-26 22:01:08,070 - __main__ - INFO - Processing file 60/106: sub-ATL004_task-picturetask_acq-MicXY.wav
2025-05-26 22:01:08,070 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,076 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,076 - __main__ - ERROR - Failed to process sub-ATL004_task-picturetask_acq-MicXY.wav
2025-05-26 22:01:08,076 - __main__ - INFO - Processing file 61/106: sub-ATL004_task-picturetask_acq-MicY.wav
2025-05-26 22:01:08,076 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,082 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,082 - __main__ - ERROR - Failed to process sub-ATL004_task-picturetask_acq-MicY.wav
2025-05-26 22:01:08,082 - __main__ - INFO - Processing file 62/106: sub-ATL004_task-picturetask_acq-ZA.wav
2025-05-26 22:01:08,082 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-picturetask_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,087 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,087 - __main__ - ERROR - Failed to process sub-ATL004_task-picturetask_acq-ZA.wav
2025-05-26 22:01:08,087 - __main__ - INFO - Processing file 63/106: sub-ATL004_task-pls5.screen_acq-MicX.wav
2025-05-26 22:01:08,087 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,090 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,090 - __main__ - ERROR - Failed to process sub-ATL004_task-pls5.screen_acq-MicX.wav
2025-05-26 22:01:08,090 - __main__ - INFO - Processing file 64/106: sub-ATL004_task-pls5.screen_acq-MicXY.wav
2025-05-26 22:01:08,090 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,094 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,094 - __main__ - ERROR - Failed to process sub-ATL004_task-pls5.screen_acq-MicXY.wav
2025-05-26 22:01:08,094 - __main__ - INFO - Processing file 65/106: sub-ATL004_task-pls5.screen_acq-MicY.wav
2025-05-26 22:01:08,094 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,100 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,101 - __main__ - ERROR - Failed to process sub-ATL004_task-pls5.screen_acq-MicY.wav
2025-05-26 22:01:08,101 - __main__ - INFO - Processing file 66/106: sub-ATL004_task-pls5.screen_acq-ZA.wav
2025-05-26 22:01:08,101 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-pls5.screen_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,106 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,106 - __main__ - ERROR - Failed to process sub-ATL004_task-pls5.screen_acq-ZA.wav
2025-05-26 22:01:08,106 - __main__ - INFO - Processing file 67/106: sub-ATL004_task-toyplay_acq-MicX.wav
2025-05-26 22:01:08,106 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,111 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,112 - __main__ - ERROR - Failed to process sub-ATL004_task-toyplay_acq-MicX.wav
2025-05-26 22:01:08,112 - __main__ - INFO - Processing file 68/106: sub-ATL004_task-toyplay_acq-MicXY.wav
2025-05-26 22:01:08,112 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,116 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,116 - __main__ - ERROR - Failed to process sub-ATL004_task-toyplay_acq-MicXY.wav
2025-05-26 22:01:08,116 - __main__ - INFO - Processing file 69/106: sub-ATL004_task-toyplay_acq-MicY.wav
2025-05-26 22:01:08,116 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,120 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,121 - __main__ - ERROR - Failed to process sub-ATL004_task-toyplay_acq-MicY.wav
2025-05-26 22:01:08,121 - __main__ - INFO - Processing file 70/106: sub-ATL004_task-toyplay_acq-ZA.wav
2025-05-26 22:01:08,121 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-toyplay_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,126 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,127 - __main__ - ERROR - Failed to process sub-ATL004_task-toyplay_acq-ZA.wav
2025-05-26 22:01:08,127 - __main__ - INFO - Processing file 71/106: sub-ATL004_task-warmup_acq-MicX.wav
2025-05-26 22:01:08,127 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-warmup_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-warmup_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,132 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,132 - __main__ - ERROR - Failed to process sub-ATL004_task-warmup_acq-MicX.wav
2025-05-26 22:01:08,132 - __main__ - INFO - Processing file 72/106: sub-ATL004_task-warmup_acq-MicXY.wav
2025-05-26 22:01:08,132 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-warmup_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-warmup_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,137 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,137 - __main__ - ERROR - Failed to process sub-ATL004_task-warmup_acq-MicXY.wav
2025-05-26 22:01:08,137 - __main__ - INFO - Processing file 73/106: sub-ATL004_task-warmup_acq-MicY.wav
2025-05-26 22:01:08,138 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-warmup_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-warmup_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,142 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,143 - __main__ - ERROR - Failed to process sub-ATL004_task-warmup_acq-MicY.wav
2025-05-26 22:01:08,143 - __main__ - INFO - Processing file 74/106: sub-ATL004_task-warmup_acq-ZA.wav
2025-05-26 22:01:08,143 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL004/beh/sub-ATL004_task-warmup_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL004/beh/sub-ATL004_task-warmup_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,149 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,149 - __main__ - ERROR - Failed to process sub-ATL004_task-warmup_acq-ZA.wav
2025-05-26 22:01:08,149 - __main__ - INFO - Processing file 75/106: sub-ATL005_task-celfp3.screen_acq-MicX.wav
2025-05-26 22:01:08,149 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,154 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,154 - __main__ - ERROR - Failed to process sub-ATL005_task-celfp3.screen_acq-MicX.wav
2025-05-26 22:01:08,154 - __main__ - INFO - Processing file 76/106: sub-ATL005_task-celfp3.screen_acq-MicXY.wav
2025-05-26 22:01:08,154 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,157 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,158 - __main__ - ERROR - Failed to process sub-ATL005_task-celfp3.screen_acq-MicXY.wav
2025-05-26 22:01:08,158 - __main__ - INFO - Processing file 77/106: sub-ATL005_task-celfp3.screen_acq-MicY.wav
2025-05-26 22:01:08,158 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,164 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,164 - __main__ - ERROR - Failed to process sub-ATL005_task-celfp3.screen_acq-MicY.wav
2025-05-26 22:01:08,164 - __main__ - INFO - Processing file 78/106: sub-ATL005_task-celfp3.screen_acq-ZA.wav
2025-05-26 22:01:08,164 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.screen_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,170 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,170 - __main__ - ERROR - Failed to process sub-ATL005_task-celfp3.screen_acq-ZA.wav
2025-05-26 22:01:08,170 - __main__ - INFO - Processing file 79/106: sub-ATL005_task-celfp3.test.2_acq-MicX.wav
2025-05-26 22:01:08,170 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test.2_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test.2_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,176 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,176 - __main__ - ERROR - Failed to process sub-ATL005_task-celfp3.test.2_acq-MicX.wav
2025-05-26 22:01:08,176 - __main__ - INFO - Processing file 80/106: sub-ATL005_task-celfp3.test.2_acq-MicXY.wav
2025-05-26 22:01:08,176 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test.2_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test.2_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,181 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,189 - __main__ - ERROR - Failed to process sub-ATL005_task-celfp3.test.2_acq-MicXY.wav
2025-05-26 22:01:08,189 - __main__ - INFO - Processing file 81/106: sub-ATL005_task-celfp3.test.2_acq-MicY.wav
2025-05-26 22:01:08,189 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test.2_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test.2_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,193 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,194 - __main__ - ERROR - Failed to process sub-ATL005_task-celfp3.test.2_acq-MicY.wav
2025-05-26 22:01:08,194 - __main__ - INFO - Processing file 82/106: sub-ATL005_task-celfp3.test.NA_acq-ZA.wav
2025-05-26 22:01:08,194 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test.NA_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test.NA_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,200 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,201 - __main__ - ERROR - Failed to process sub-ATL005_task-celfp3.test.NA_acq-ZA.wav
2025-05-26 22:01:08,201 - __main__ - INFO - Processing file 83/106: sub-ATL005_task-celfp3.test_acq-MicX.wav
2025-05-26 22:01:08,201 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,205 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,206 - __main__ - ERROR - Failed to process sub-ATL005_task-celfp3.test_acq-MicX.wav
2025-05-26 22:01:08,206 - __main__ - INFO - Processing file 84/106: sub-ATL005_task-celfp3.test_acq-MicXY.wav
2025-05-26 22:01:08,206 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,212 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,212 - __main__ - ERROR - Failed to process sub-ATL005_task-celfp3.test_acq-MicXY.wav
2025-05-26 22:01:08,212 - __main__ - INFO - Processing file 85/106: sub-ATL005_task-celfp3.test_acq-MicY.wav
2025-05-26 22:01:08,212 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,217 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,217 - __main__ - ERROR - Failed to process sub-ATL005_task-celfp3.test_acq-MicY.wav
2025-05-26 22:01:08,217 - __main__ - INFO - Processing file 86/106: sub-ATL005_task-celfp3.test_acq-ZA.wav
2025-05-26 22:01:08,217 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-celfp3.test_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,222 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,222 - __main__ - ERROR - Failed to process sub-ATL005_task-celfp3.test_acq-ZA.wav
2025-05-26 22:01:08,222 - __main__ - INFO - Processing file 87/106: sub-ATL005_task-gfta.test_acq-MicX.wav
2025-05-26 22:01:08,223 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,229 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,229 - __main__ - ERROR - Failed to process sub-ATL005_task-gfta.test_acq-MicX.wav
2025-05-26 22:01:08,229 - __main__ - INFO - Processing file 88/106: sub-ATL005_task-gfta.test_acq-MicXY.wav
2025-05-26 22:01:08,229 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,234 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,234 - __main__ - ERROR - Failed to process sub-ATL005_task-gfta.test_acq-MicXY.wav
2025-05-26 22:01:08,234 - __main__ - INFO - Processing file 89/106: sub-ATL005_task-gfta.test_acq-MicY.wav
2025-05-26 22:01:08,234 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,239 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,239 - __main__ - ERROR - Failed to process sub-ATL005_task-gfta.test_acq-MicY.wav
2025-05-26 22:01:08,239 - __main__ - INFO - Processing file 90/106: sub-ATL005_task-gfta.test_acq-ZA.wav
2025-05-26 22:01:08,239 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-gfta.test_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,243 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,243 - __main__ - ERROR - Failed to process sub-ATL005_task-gfta.test_acq-ZA.wav
2025-05-26 22:01:08,243 - __main__ - INFO - Processing file 91/106: sub-ATL005_task-picturetask_acq-MicX.wav
2025-05-26 22:01:08,243 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,249 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,250 - __main__ - ERROR - Failed to process sub-ATL005_task-picturetask_acq-MicX.wav
2025-05-26 22:01:08,250 - __main__ - INFO - Processing file 92/106: sub-ATL005_task-picturetask_acq-MicXY.wav
2025-05-26 22:01:08,250 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,254 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,255 - __main__ - ERROR - Failed to process sub-ATL005_task-picturetask_acq-MicXY.wav
2025-05-26 22:01:08,255 - __main__ - INFO - Processing file 93/106: sub-ATL005_task-picturetask_acq-MicY.wav
2025-05-26 22:01:08,255 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,258 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,258 - __main__ - ERROR - Failed to process sub-ATL005_task-picturetask_acq-MicY.wav
2025-05-26 22:01:08,258 - __main__ - INFO - Processing file 94/106: sub-ATL005_task-picturetask_acq-ZA.wav
2025-05-26 22:01:08,258 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-picturetask_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,261 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,262 - __main__ - ERROR - Failed to process sub-ATL005_task-picturetask_acq-ZA.wav
2025-05-26 22:01:08,262 - __main__ - INFO - Processing file 95/106: sub-ATL005_task-pls5.screen_acq-MicX.wav
2025-05-26 22:01:08,262 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,269 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,269 - __main__ - ERROR - Failed to process sub-ATL005_task-pls5.screen_acq-MicX.wav
2025-05-26 22:01:08,269 - __main__ - INFO - Processing file 96/106: sub-ATL005_task-pls5.screen_acq-MicXY.wav
2025-05-26 22:01:08,269 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,274 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,274 - __main__ - ERROR - Failed to process sub-ATL005_task-pls5.screen_acq-MicXY.wav
2025-05-26 22:01:08,274 - __main__ - INFO - Processing file 97/106: sub-ATL005_task-pls5.screen_acq-MicY.wav
2025-05-26 22:01:08,274 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,278 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,278 - __main__ - ERROR - Failed to process sub-ATL005_task-pls5.screen_acq-MicY.wav
2025-05-26 22:01:08,278 - __main__ - INFO - Processing file 98/106: sub-ATL005_task-pls5.screen_acq-ZA.wav
2025-05-26 22:01:08,279 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-pls5.screen_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,281 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,282 - __main__ - ERROR - Failed to process sub-ATL005_task-pls5.screen_acq-ZA.wav
2025-05-26 22:01:08,282 - __main__ - INFO - Processing file 99/106: sub-ATL005_task-toyplay_acq-MicX.wav
2025-05-26 22:01:08,282 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,285 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,285 - __main__ - ERROR - Failed to process sub-ATL005_task-toyplay_acq-MicX.wav
2025-05-26 22:01:08,285 - __main__ - INFO - Processing file 100/106: sub-ATL005_task-toyplay_acq-MicXY.wav
2025-05-26 22:01:08,285 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,288 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,288 - __main__ - ERROR - Failed to process sub-ATL005_task-toyplay_acq-MicXY.wav
2025-05-26 22:01:08,288 - __main__ - INFO - Processing file 101/106: sub-ATL005_task-toyplay_acq-MicY.wav
2025-05-26 22:01:08,288 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,294 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,294 - __main__ - ERROR - Failed to process sub-ATL005_task-toyplay_acq-MicY.wav
2025-05-26 22:01:08,294 - __main__ - INFO - Processing file 102/106: sub-ATL005_task-toyplay_acq-ZA.wav
2025-05-26 22:01:08,294 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-toyplay_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,299 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,299 - __main__ - ERROR - Failed to process sub-ATL005_task-toyplay_acq-ZA.wav
2025-05-26 22:01:08,299 - __main__ - INFO - Processing file 103/106: sub-ATL005_task-warmup_acq-MicX.wav
2025-05-26 22:01:08,299 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-warmup_acq-MicX.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-warmup_acq-MicX.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,304 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,305 - __main__ - ERROR - Failed to process sub-ATL005_task-warmup_acq-MicX.wav
2025-05-26 22:01:08,305 - __main__ - INFO - Processing file 104/106: sub-ATL005_task-warmup_acq-MicXY.wav
2025-05-26 22:01:08,305 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-warmup_acq-MicXY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-warmup_acq-MicXY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,308 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,308 - __main__ - ERROR - Failed to process sub-ATL005_task-warmup_acq-MicXY.wav
2025-05-26 22:01:08,308 - __main__ - INFO - Processing file 105/106: sub-ATL005_task-warmup_acq-MicY.wav
2025-05-26 22:01:08,308 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-warmup_acq-MicY.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-warmup_acq-MicY.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,311 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,311 - __main__ - ERROR - Failed to process sub-ATL005_task-warmup_acq-MicY.wav
2025-05-26 22:01:08,311 - __main__ - INFO - Processing file 106/106: sub-ATL005_task-warmup_acq-ZA.wav
2025-05-26 22:01:08,311 - __main__ - INFO - Running command: python -m audtecheq.cli.main preprocess --input audtecheq/data/sub-ATL005/beh/sub-ATL005_task-warmup_acq-ZA.wav --output audtecheq/data/derivative/preprocessed/sub-ATL005/beh/sub-ATL005_task-warmup_acq-ZA.wav --sample-rate 16000 --channels 1 --target-dbfs -20.0 --noise-reduction 0.8 --log-level INFO
2025-05-26 22:01:08,314 - __main__ - ERROR - Failed to run command: [Errno 2] No such file or directory: 'python'
2025-05-26 22:01:08,314 - __main__ - ERROR - Failed to process sub-ATL005_task-warmup_acq-ZA.wav
2025-05-26 22:01:08,316 - __main__ - INFO - Processing report saved to: audtecheq/data/derivative/reports/preprocessing_report_20250526_220107.json
2025-05-26 22:01:08,316 - __main__ - INFO - Summary: 0/106 files processed successfully
2025-05-26 22:01:08,316 - __main__ - INFO - Total processing time: 0.53s
2025-05-26 22:01:08,316 - __main__ - INFO - Average processing time: 0.01s per file
2025-05-26 22:01:08,316 - __main__ - INFO - Preprocessing completed!
2025-05-26 22:01:08,316 - __main__ - INFO - Successfully processed: 0/106 files
2025-05-26 22:01:08,316 - __main__ - INFO - Log file: preprocessing_log_20250526_220107.log
2025-05-26 22:01:08,316 - __main__ - INFO - Report file: audtecheq/data/derivative/reports/preprocessing_report_20250526_220107.json
